<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下拉菜单测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f7;
        }
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }
        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .console-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 下拉菜单修复测试页面</h1>
    
    <div class="test-section">
        <h2>📋 联系人下拉菜单测试</h2>
        <div class="form-group">
            <label>群聊/联系人</label>
            <select id="talker-select">
                <option value="">全部</option>
            </select>
        </div>
        <button class="btn" onclick="testContactsLoad()">加载联系人</button>
        <button class="btn" onclick="checkContactsCount()">检查数量</button>
    </div>

    <div class="test-section">
        <h2>👥 群聊下拉菜单测试</h2>
        <div class="form-group">
            <label>群聊名称</label>
            <select id="analysis-group">
                <option value="">请选择群聊</option>
            </select>
        </div>
        <button class="btn" onclick="testGroupsLoad()">加载群聊</button>
        <button class="btn" onclick="checkGroupsCount()">检查数量</button>
    </div>

    <div class="test-section">
        <h2>🖥️ 控制台输出</h2>
        <div id="console-output" class="console-output">等待测试结果...\n</div>
        <button class="btn" onclick="clearConsole()">清空输出</button>
        <button class="btn" onclick="runAllTests()">运行所有测试</button>
    </div>

    <script>
        // 模拟控制台输出
        function log(message) {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            output.textContent += `[${timestamp}] ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        // API请求函数
        async function apiRequest(url) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                return await response.json();
            } catch (error) {
                log(`❌ API请求失败: ${error.message}`);
                throw error;
            }
        }

        // 填充联系人选择框
        function populateContactSelect(contacts) {
            const talkerSelect = document.getElementById('talker-select');
            if (!talkerSelect) {
                log('❌ 找不到talker-select元素');
                return;
            }

            // 清空现有选项
            talkerSelect.innerHTML = '<option value="">全部</option>';

            // 过滤和排序联系人
            const filteredContacts = contacts
                .filter(contact => contact.userName && contact.nickName)
                .sort((a, b) => (a.nickName || '').localeCompare(b.nickName || ''));

            filteredContacts.forEach(contact => {
                const option = document.createElement('option');
                option.value = contact.userName;
                const displayName = contact.remark ? 
                    `${contact.nickName} (${contact.remark})` : 
                    contact.nickName;
                option.textContent = displayName;
                talkerSelect.appendChild(option);
            });

            log(`✅ 已加载 ${filteredContacts.length} 个联系人到下拉菜单`);
        }

        // 填充群聊选择框
        function populateGroupSelect(chatrooms) {
            const analysisGroupSelect = document.getElementById('analysis-group');
            if (!analysisGroupSelect) {
                log('❌ 找不到analysis-group元素');
                return;
            }

            // 清空现有选项
            analysisGroupSelect.innerHTML = '<option value="">请选择群聊</option>';

            // 过滤和排序群聊
            const filteredChatrooms = chatrooms
                .filter(chatroom => chatroom.name && chatroom.nickName)
                .sort((a, b) => {
                    const aUserCount = (a.users && a.users.length) || 0;
                    const bUserCount = (b.users && b.users.length) || 0;
                    return bUserCount - aUserCount;
                });

            filteredChatrooms.forEach(chatroom => {
                const option = document.createElement('option');
                option.value = chatroom.name;
                const memberCount = (chatroom.users && chatroom.users.length) || 0;
                option.textContent = `${chatroom.nickName} (${memberCount}人)`;
                analysisGroupSelect.appendChild(option);
            });

            log(`✅ 已加载 ${filteredChatrooms.length} 个群聊到下拉菜单`);
        }

        // 测试函数
        async function testContactsLoad() {
            log('📞 开始测试联系人加载...');
            try {
                const response = await apiRequest('http://127.0.0.1:3000/api/chatlog/contacts');
                log(`📋 联系人API响应: success=${response.success}, 数据量=${response.data?.length || 0}`);
                
                if (response.success) {
                    populateContactSelect(response.data || []);
                } else {
                    log(`❌ 联系人API失败: ${response.message}`);
                }
            } catch (error) {
                log(`❌ 联系人测试失败: ${error.message}`);
            }
        }

        async function testGroupsLoad() {
            log('👥 开始测试群聊加载...');
            try {
                const response = await apiRequest('http://127.0.0.1:3000/api/chatlog/chatrooms');
                log(`👥 群聊API响应: success=${response.success}, 数据量=${response.data?.length || 0}`);
                
                if (response.success) {
                    populateGroupSelect(response.data || []);
                } else {
                    log(`❌ 群聊API失败: ${response.message}`);
                }
            } catch (error) {
                log(`❌ 群聊测试失败: ${error.message}`);
            }
        }

        function checkContactsCount() {
            const select = document.getElementById('talker-select');
            log(`📊 联系人下拉菜单选项数量: ${select.options.length}`);
        }

        function checkGroupsCount() {
            const select = document.getElementById('analysis-group');
            log(`📊 群聊下拉菜单选项数量: ${select.options.length}`);
        }

        function clearConsole() {
            document.getElementById('console-output').textContent = '';
        }

        async function runAllTests() {
            log('🚀 开始运行所有测试...');
            await testContactsLoad();
            await testGroupsLoad();
            checkContactsCount();
            checkGroupsCount();
            log('✅ 所有测试完成');
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            log('🌐 测试页面已加载');
            log('💡 点击按钮开始测试，或者运行所有测试');
        });
    </script>
</body>
</html>
