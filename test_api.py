#!/usr/bin/env python3
"""
简单的API测试脚本
"""
import requests
import json

BASE_URL = "http://localhost:3000"

def test_health_check():
    """测试健康检查接口"""
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"健康检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"健康检查失败: {e}")
        return False

def test_chatlog_status():
    """测试Chatlog状态检查"""
    try:
        response = requests.get(f"{BASE_URL}/api/chatlog/status")
        print(f"Chatlog状态检查: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"Chatlog状态检查失败: {e}")
        return False

def main():
    print("开始API测试...")
    print("=" * 50)

    # 测试健康检查
    print("1. 测试健康检查接口")
    if test_health_check():
        print("✅ 健康检查通过")
    else:
        print("❌ 健康检查失败")

    print()

    # 测试Chatlog状态
    print("2. 测试Chatlog状态检查")
    if test_chatlog_status():
        print("✅ Chatlog状态检查完成")
    else:
        print("❌ Chatlog状态检查失败")

    print("=" * 50)
    print("API测试完成")

if __name__ == "__main__":
    main()