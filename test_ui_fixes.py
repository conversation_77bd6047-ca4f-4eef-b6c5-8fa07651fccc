#!/usr/bin/env python3
"""
测试UI修复功能
"""
import requests
import json
from datetime import datetime

def test_chatlog_query_simplification():
    """测试聊天记录查询简化"""
    print("📋 测试聊天记录查询简化")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 测试简化后的查询API
    test_cases = [
        ("全部查询", {"time": "2025-07-20~2025-07-20", "talker": "all", "limit": "5"}),
        ("限制数量查询", {"time": "2025-07-20~2025-07-20", "talker": "all", "limit": "10"}),
    ]
    
    for test_name, params in test_cases:
        try:
            url = f"{base_url}/api/chatlog/chatlog"
            response = requests.get(url, params=params, proxies=proxies, timeout=10)
            data = response.json()
            
            if data.get('success'):
                record_count = len(data.get('data', []))
                print(f"✅ {test_name}: 成功 ({record_count} 条记录)")
                
                if 'pagination' in data:
                    pagination = data['pagination']
                    print(f"   分页信息: 总数={pagination.get('total')}, 页面={pagination.get('page')}")
            else:
                print(f"❌ {test_name}: 失败 - {data.get('message')}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")

def test_ai_analysis_api():
    """测试AI分析API"""
    print("\n🤖 测试AI分析API")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 测试AI分析API端点
    try:
        url = f"{base_url}/api/ai/analyze"
        test_data = {
            "group_id": "test_group@chatroom",
            "group_name": "测试群聊",
            "analysis_type": "general"
        }
        
        response = requests.post(
            url, 
            json=test_data, 
            proxies=proxies, 
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ AI分析API响应: {data}")
        elif response.status_code == 404:
            print("⚠️  AI分析API端点未找到 (404) - 这是预期的，因为后端可能未实现")
        else:
            print(f"❌ AI分析API异常状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到AI分析API")
    except Exception as e:
        print(f"❌ AI分析API测试异常: {str(e)}")

def generate_frontend_test_guide():
    """生成前端测试指南"""
    print("\n🖥️  前端测试指南")
    print("=" * 50)
    
    print("请在浏览器中测试以下功能：")
    print()
    print("1. 📋 聊天记录查询页面测试:")
    print("   - 访问 http://127.0.0.1:3000")
    print("   - 切换到'聊天记录'标签页")
    print("   - 验证只有一个'查询类型'下拉菜单（全部/群聊/联系人）")
    print("   - 验证没有'具体选择'下拉菜单")
    print("   - 设置时间范围和记录数量")
    print("   - 点击查询按钮")
    print("   - 验证查询结果显示在表单下方")
    print()
    print("2. 🤖 AI分析页面测试:")
    print("   - 切换到'AI分析'标签页")
    print("   - 验证'群聊名称'是输入框而不是下拉菜单")
    print("   - 在输入框中输入群聊名称（如'AI'）")
    print("   - 验证出现自动补全下拉菜单")
    print("   - 使用键盘上下键导航选项")
    print("   - 点击选择一个群聊")
    print("   - 选择分析类型")
    print("   - 点击'开始分析'按钮")
    print("   - 验证按钮状态变为'分析中...'")
    print("   - 验证显示适当的提示信息")
    print()
    print("3. 🔍 自动补全功能测试:")
    print("   在AI分析页面的群聊名称输入框中:")
    print("   - 输入'AI' - 应显示包含AI的群聊")
    print("   - 输入'群' - 应显示包含'群'字的群聊")
    print("   - 使用上下箭头键导航")
    print("   - 按Enter键选择")
    print("   - 按Escape键关闭下拉菜单")
    print("   - 点击外部区域关闭下拉菜单")
    print()
    print("4. 🛠️  开发者工具测试:")
    print("   打开浏览器开发者工具，在控制台运行:")
    print()
    print("   // 测试自动补全初始化")
    print("   ChatlogApp.initAnalysisAutocomplete();")
    print()
    print("   // 测试AI分析提交")
    print("   const form = document.getElementById('analysis-form');")
    print("   const event = new Event('submit');")
    print("   form.dispatchEvent(event);")
    print()
    print("   // 检查隐藏输入框的值")
    print("   const hiddenInput = document.getElementById('analysis-group');")
    print("   console.log('选中的群聊ID:', hiddenInput.value);")

def generate_css_test():
    """生成CSS样式测试"""
    print("\n🎨 CSS样式测试")
    print("=" * 50)
    
    print("请验证以下样式效果:")
    print()
    print("1. 自动补全输入框:")
    print("   - 输入框有正确的边框和圆角")
    print("   - 聚焦时有蓝色边框和阴影效果")
    print("   - 下拉菜单正确定位在输入框下方")
    print()
    print("2. 自动补全下拉菜单:")
    print("   - 白色背景，有边框")
    print("   - 最大高度200px，超出时可滚动")
    print("   - 鼠标悬停时项目背景变色")
    print("   - 键盘选中时项目有选中样式")
    print()
    print("3. 加载状态:")
    print("   - 查询时显示'正在查询聊天记录...'")
    print("   - AI分析时按钮文字变为'分析中...'")
    print("   - 按钮在处理时被禁用")
    print()
    print("4. 响应式设计:")
    print("   - 在不同屏幕尺寸下布局正常")
    print("   - 移动设备上触摸交互正常")

def main():
    """主函数"""
    print("🔧 UI修复功能测试")
    print("=" * 60)
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试聊天记录查询简化
    test_chatlog_query_simplification()
    
    # 测试AI分析API
    test_ai_analysis_api()
    
    # 生成前端测试指南
    generate_frontend_test_guide()
    
    # 生成CSS样式测试
    generate_css_test()
    
    print("\n📋 修复总结:")
    print("1. ✅ 移除了'具体选择'下拉菜单，简化了聊天记录查询")
    print("2. ✅ 保留了'查询类型'下拉菜单，支持全部/群聊/联系人选择")
    print("3. ✅ 将AI分析的群聊选择改为自动补全输入框")
    print("4. ✅ 实现了键盘导航和鼠标交互的自动补全功能")
    print("5. ✅ 改进了AI分析的错误处理和用户反馈")
    print("6. ✅ 添加了加载状态和按钮禁用逻辑")
    print("7. ✅ 优化了CSS样式，提升了用户体验")
    print()
    print("🌐 请访问 http://127.0.0.1:3000 测试修复后的功能")

if __name__ == "__main__":
    main()
