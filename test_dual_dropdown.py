#!/usr/bin/env python3
"""
测试双下拉菜单功能
"""
import requests
import json
from datetime import datetime

def test_api_endpoints():
    """测试API端点"""
    print("🔍 测试API端点")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 测试联系人API
    try:
        response = requests.get(f"{base_url}/api/chatlog/contacts", proxies=proxies, timeout=10)
        contacts_data = response.json()
        
        if contacts_data.get('success'):
            contacts = contacts_data.get('data', [])
            valid_contacts = [c for c in contacts if c.get('userName') and c.get('nickName')]
            print(f"✅ 联系人API: 总数 {len(contacts)}, 有效 {len(valid_contacts)}")
            
            # 显示前3个联系人
            print("   前3个联系人:")
            for i, contact in enumerate(valid_contacts[:3]):
                display_name = contact.get('remark') or contact.get('nickName', 'Unknown')
                print(f"     {i+1}. {display_name} ({contact.get('userName')})")
        else:
            print(f"❌ 联系人API失败: {contacts_data.get('message')}")
    except Exception as e:
        print(f"❌ 联系人API异常: {str(e)}")
    
    print()
    
    # 测试群聊API
    try:
        response = requests.get(f"{base_url}/api/chatlog/chatrooms", proxies=proxies, timeout=10)
        chatrooms_data = response.json()
        
        if chatrooms_data.get('success'):
            chatrooms = chatrooms_data.get('data', [])
            valid_chatrooms = [c for c in chatrooms if c.get('name') and c.get('nickName')]
            print(f"✅ 群聊API: 总数 {len(chatrooms)}, 有效 {len(valid_chatrooms)}")
            
            # 按成员数量排序
            sorted_chatrooms = sorted(valid_chatrooms, 
                                    key=lambda x: len(x.get('users', [])) if x.get('users') else 0, 
                                    reverse=True)
            
            print("   按成员数量排序的前3个群聊:")
            for i, chatroom in enumerate(sorted_chatrooms[:3]):
                member_count = len(chatroom.get('users', [])) if chatroom.get('users') else 0
                print(f"     {i+1}. {chatroom.get('nickName')} ({member_count}人)")
        else:
            print(f"❌ 群聊API失败: {chatrooms_data.get('message')}")
    except Exception as e:
        print(f"❌ 群聊API异常: {str(e)}")

def test_chatlog_query():
    """测试聊天记录查询"""
    print("\n💬 测试聊天记录查询")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 测试不同的查询参数
    test_cases = [
        ("全部查询", {"time": "2025-07-20~2025-07-20", "limit": "5"}),
        ("指定群聊", {"time": "2025-07-20~2025-07-20", "talker": "10597257184@chatroom", "limit": "5"}),
    ]
    
    for test_name, params in test_cases:
        try:
            url = f"{base_url}/api/chatlog/chatlog"
            response = requests.get(url, params=params, proxies=proxies, timeout=10)
            data = response.json()
            
            if data.get('success'):
                record_count = len(data.get('data', []))
                print(f"✅ {test_name}: 成功 ({record_count} 条记录)")
                
                if 'pagination' in data:
                    pagination = data['pagination']
                    print(f"   分页信息: 总数={pagination.get('total')}, 页面={pagination.get('page')}")
            else:
                print(f"❌ {test_name}: 失败 - {data.get('message')}")
        except Exception as e:
            print(f"❌ {test_name}: 异常 - {str(e)}")

def generate_frontend_test_guide():
    """生成前端测试指南"""
    print("\n🖥️  前端测试指南")
    print("=" * 50)
    
    print("请在浏览器中测试以下功能：")
    print()
    print("1. 📋 基本功能测试:")
    print("   - 访问 http://127.0.0.1:3000")
    print("   - 切换到'聊天记录'标签页")
    print("   - 查看新的双下拉菜单设计")
    print()
    print("2. 🔄 查询类型选择测试:")
    print("   - 第一个下拉菜单选择'全部' - 第二个下拉菜单应显示'全部'")
    print("   - 第一个下拉菜单选择'群聊' - 第二个下拉菜单应加载群聊列表")
    print("   - 第一个下拉菜单选择'联系人' - 第二个下拉菜单应加载联系人列表")
    print()
    print("3. 📊 数据验证测试:")
    print("   - 群聊列表应按成员数量从高到低排序")
    print("   - 联系人列表应按昵称字母顺序排序")
    print("   - 群聊显示格式: '群聊名称 (成员数人)'")
    print("   - 联系人显示格式: '昵称' 或 '昵称 (备注)'")
    print()
    print("4. 🔍 查询功能测试:")
    print("   - 选择不同的查询类型和具体项目")
    print("   - 设置时间范围")
    print("   - 点击查询按钮")
    print("   - 验证查询结果")
    print()
    print("5. 🛠️  开发者工具测试:")
    print("   打开浏览器开发者工具，在控制台运行:")
    print()
    print("   // 测试查询类型变更")
    print("   const typeSelect = document.getElementById('talker-type-select');")
    print("   typeSelect.value = 'chatroom';")
    print("   typeSelect.dispatchEvent(new Event('change'));")
    print()
    print("   // 检查第二个下拉菜单状态")
    print("   const talkerSelect = document.getElementById('talker-select');")
    print("   console.log('第二个下拉菜单选项数量:', talkerSelect.options.length);")
    print("   console.log('是否禁用:', talkerSelect.disabled);")
    print()
    print("   // 手动触发数据加载")
    print("   ChatlogApp.loadChatroomsForSelect();  // 加载群聊")
    print("   ChatlogApp.loadContactsForSelect();   // 加载联系人")

def main():
    """主函数"""
    print("🔧 双下拉菜单功能测试")
    print("=" * 60)
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试API端点
    test_api_endpoints()
    
    # 测试聊天记录查询
    test_chatlog_query()
    
    # 生成前端测试指南
    generate_frontend_test_guide()
    
    print("\n📋 功能改进总结:")
    print("1. ✅ 将单个下拉菜单改为双下拉菜单设计")
    print("2. ✅ 第一个下拉菜单：选择查询类型（全部/群聊/联系人）")
    print("3. ✅ 第二个下拉菜单：根据类型动态加载相应数据")
    print("4. ✅ 群聊按成员数量排序，联系人按昵称排序")
    print("5. ✅ 添加了加载状态和禁用状态样式")
    print("6. ✅ 改进了用户体验和交互逻辑")
    print()
    print("🌐 请访问 http://127.0.0.1:3000 测试新功能")

if __name__ == "__main__":
    main()
