<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>双下拉菜单功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f7;
        }
        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 14px;
        }
        select:disabled {
            background: #f5f5f7;
            color: #86868b;
            cursor: not-allowed;
        }
        .btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin-top: 10px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .log {
            background: #1e1e1e;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 双下拉菜单功能演示</h1>
    
    <div class="demo-section">
        <h2>📋 聊天记录查询 - 双下拉菜单</h2>
        <p>这是新的双下拉菜单设计，第一个选择查询类型，第二个根据类型动态加载数据。</p>
        
        <div class="form-group">
            <label>查询类型</label>
            <select id="talker-type-select">
                <option value="">全部</option>
                <option value="chatroom">群聊</option>
                <option value="contact">联系人</option>
            </select>
        </div>

        <div class="form-group">
            <label>具体选择</label>
            <select id="talker-select" disabled>
                <option value="">请先选择查询类型</option>
            </select>
        </div>

        <button class="btn" onclick="testTypeChange()">测试类型变更</button>
        <button class="btn" onclick="loadTestData()">加载测试数据</button>
        <button class="btn" onclick="clearLog()">清空日志</button>

        <div id="status" class="status info">等待操作...</div>
    </div>

    <div class="demo-section">
        <h2>📊 功能特性展示</h2>
        <ul>
            <li><strong>智能联动</strong>：第一个下拉菜单变化时，第二个自动更新</li>
            <li><strong>数据排序</strong>：群聊按成员数量排序，联系人按昵称排序</li>
            <li><strong>加载状态</strong>：显示"加载中..."状态，禁用交互</li>
            <li><strong>错误处理</strong>：网络错误时显示"加载失败"</li>
            <li><strong>用户体验</strong>：流畅的交互，清晰的视觉反馈</li>
        </ul>
    </div>

    <div class="demo-section">
        <h2>🖥️ 操作日志</h2>
        <div id="log" class="log">等待操作...\n</div>
    </div>

    <script>
        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // 状态更新函数
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        // API请求函数
        async function apiRequest(url) {
            try {
                const response = await fetch(url);
                return await response.json();
            } catch (error) {
                log(`❌ API请求失败: ${error.message}`);
                throw error;
            }
        }

        // 处理查询类型变化
        async function handleTalkerTypeChange(event) {
            const talkerType = event.target.value;
            const talkerSelect = document.getElementById('talker-select');
            
            log(`🔄 查询类型变更为: ${talkerType || '全部'}`);
            updateStatus('正在加载数据...', 'info');
            
            // 重置第二个下拉菜单
            talkerSelect.innerHTML = '<option value="">加载中...</option>';
            talkerSelect.disabled = true;
            
            try {
                if (talkerType === 'chatroom') {
                    await loadChatroomsForSelect();
                } else if (talkerType === 'contact') {
                    await loadContactsForSelect();
                } else {
                    talkerSelect.innerHTML = '<option value="">全部</option>';
                    talkerSelect.disabled = false;
                    log('✅ 设置为全部查询');
                    updateStatus('已设置为全部查询', 'success');
                }
            } catch (error) {
                log(`❌ 加载数据失败: ${error.message}`);
                talkerSelect.innerHTML = '<option value="">加载失败</option>';
                talkerSelect.disabled = false;
                updateStatus('加载数据失败', 'error');
            }
        }

        // 加载群聊列表
        async function loadChatroomsForSelect() {
            log('👥 正在加载群聊列表...');
            
            const response = await apiRequest('http://127.0.0.1:3000/api/chatlog/chatrooms');
            const talkerSelect = document.getElementById('talker-select');
            
            if (response.success) {
                const chatrooms = response.data || [];
                log(`👥 成功获取 ${chatrooms.length} 个群聊`);
                
                // 过滤和排序
                const filteredChatrooms = chatrooms
                    .filter(chatroom => chatroom.name && chatroom.nickName)
                    .sort((a, b) => {
                        const aUserCount = (a.users && Array.isArray(a.users)) ? a.users.length : 0;
                        const bUserCount = (b.users && Array.isArray(b.users)) ? b.users.length : 0;
                        return bUserCount - aUserCount;
                    });
                
                // 填充下拉菜单
                talkerSelect.innerHTML = '<option value="">请选择群聊</option>';
                
                filteredChatrooms.forEach(chatroom => {
                    const option = document.createElement('option');
                    option.value = chatroom.name;
                    const memberCount = (chatroom.users && Array.isArray(chatroom.users)) ? chatroom.users.length : 0;
                    option.textContent = `${chatroom.nickName} (${memberCount}人)`;
                    talkerSelect.appendChild(option);
                });
                
                talkerSelect.disabled = false;
                log(`✅ 已加载 ${filteredChatrooms.length} 个群聊到下拉菜单`);
                updateStatus(`成功加载 ${filteredChatrooms.length} 个群聊`, 'success');
            } else {
                throw new Error(response.message || '群聊API失败');
            }
        }

        // 加载联系人列表
        async function loadContactsForSelect() {
            log('📋 正在加载联系人列表...');
            
            const response = await apiRequest('http://127.0.0.1:3000/api/chatlog/contacts');
            const talkerSelect = document.getElementById('talker-select');
            
            if (response.success) {
                const contacts = response.data || [];
                log(`📋 成功获取 ${contacts.length} 个联系人`);
                
                // 过滤和排序
                const filteredContacts = contacts
                    .filter(contact => contact.userName && contact.nickName)
                    .sort((a, b) => (a.nickName || '').localeCompare(b.nickName || ''));
                
                // 填充下拉菜单
                talkerSelect.innerHTML = '<option value="">请选择联系人</option>';
                
                filteredContacts.forEach(contact => {
                    const option = document.createElement('option');
                    option.value = contact.userName;
                    const displayName = contact.remark ? 
                        `${contact.nickName} (${contact.remark})` : 
                        contact.nickName;
                    option.textContent = displayName;
                    talkerSelect.appendChild(option);
                });
                
                talkerSelect.disabled = false;
                log(`✅ 已加载 ${filteredContacts.length} 个联系人到下拉菜单`);
                updateStatus(`成功加载 ${filteredContacts.length} 个联系人`, 'success');
            } else {
                throw new Error(response.message || '联系人API失败');
            }
        }

        // 测试函数
        function testTypeChange() {
            const typeSelect = document.getElementById('talker-type-select');
            const currentValue = typeSelect.value;
            
            if (currentValue === '' || currentValue === 'contact') {
                typeSelect.value = 'chatroom';
                log('🧪 测试：切换到群聊模式');
            } else {
                typeSelect.value = 'contact';
                log('🧪 测试：切换到联系人模式');
            }
            
            typeSelect.dispatchEvent(new Event('change'));
        }

        function loadTestData() {
            log('🧪 开始加载测试数据...');
            const typeSelect = document.getElementById('talker-type-select');
            typeSelect.value = 'chatroom';
            typeSelect.dispatchEvent(new Event('change'));
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            updateStatus('日志已清空', 'info');
        }

        // 初始化
        window.addEventListener('load', () => {
            log('🌐 双下拉菜单演示页面已加载');
            log('💡 请选择查询类型来测试功能');
            
            // 绑定事件监听器
            const typeSelect = document.getElementById('talker-type-select');
            typeSelect.addEventListener('change', handleTalkerTypeChange);
            
            updateStatus('页面已就绪，请开始测试', 'success');
        });
    </script>
</body>
</html>
