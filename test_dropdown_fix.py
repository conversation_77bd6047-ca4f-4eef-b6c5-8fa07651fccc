#!/usr/bin/env python3
"""
测试下拉菜单修复
"""
import requests
import json
from datetime import datetime

def test_api_data():
    """测试API数据格式"""
    print("🔍 测试API数据格式")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 测试联系人API
    try:
        response = requests.get(f"{base_url}/api/chatlog/contacts", proxies=proxies, timeout=10)
        contacts_data = response.json()
        
        if contacts_data.get('success'):
            contacts = contacts_data.get('data', [])
            print(f"✅ 联系人API: 成功获取 {len(contacts)} 个联系人")
            
            if len(contacts) > 0:
                sample_contact = contacts[0]
                print(f"   示例联系人字段: {list(sample_contact.keys())}")
                print(f"   示例数据: userName={sample_contact.get('userName')}, nickName={sample_contact.get('nickName')}")
        else:
            print(f"❌ 联系人API失败: {contacts_data.get('message')}")
    except Exception as e:
        print(f"❌ 联系人API异常: {str(e)}")
    
    print()
    
    # 测试群聊API
    try:
        response = requests.get(f"{base_url}/api/chatlog/chatrooms", proxies=proxies, timeout=10)
        chatrooms_data = response.json()
        
        if chatrooms_data.get('success'):
            chatrooms = chatrooms_data.get('data', [])
            print(f"✅ 群聊API: 成功获取 {len(chatrooms)} 个群聊")
            
            if len(chatrooms) > 0:
                sample_chatroom = chatrooms[0]
                print(f"   示例群聊字段: {list(sample_chatroom.keys())}")
                print(f"   示例数据: name={sample_chatroom.get('name')}, nickName={sample_chatroom.get('nickName')}")
                
                # 检查成员数量
                users = sample_chatroom.get('users', [])
                print(f"   成员数量: {len(users)}")
                
                # 显示前5个群聊的成员数量
                print("\n   前5个群聊的成员数量:")
                for i, chatroom in enumerate(chatrooms[:5]):
                    member_count = len(chatroom.get('users', []))
                    print(f"   {i+1}. {chatroom.get('nickName', 'Unknown')} - {member_count}人")
        else:
            print(f"❌ 群聊API失败: {chatrooms_data.get('message')}")
    except Exception as e:
        print(f"❌ 群聊API异常: {str(e)}")

def test_javascript_console():
    """提供JavaScript控制台测试代码"""
    print("\n🖥️  前端测试指南")
    print("=" * 50)
    print("请在浏览器开发者工具的控制台中运行以下代码来测试下拉菜单填充：")
    print()
    print("1. 测试联系人数据加载:")
    print("   ChatlogApp.apiRequest('/api/chatlog/contacts').then(data => {")
    print("       console.log('联系人数据:', data);")
    print("       if (data.success) {")
    print("           console.log('联系人数量:', data.data.length);")
    print("           console.log('第一个联系人:', data.data[0]);")
    print("       }")
    print("   });")
    print()
    print("2. 测试群聊数据加载:")
    print("   ChatlogApp.apiRequest('/api/chatlog/chatrooms').then(data => {")
    print("       console.log('群聊数据:', data);")
    print("       if (data.success) {")
    print("           console.log('群聊数量:', data.data.length);")
    print("           console.log('第一个群聊:', data.data[0]);")
    print("       }")
    print("   });")
    print()
    print("3. 手动触发数据加载:")
    print("   // 重新加载联系人和群聊数据")
    print("   loadContactsAndGroups();")
    print()
    print("4. 检查下拉菜单元素:")
    print("   console.log('联系人下拉菜单:', document.getElementById('talker-select'));")
    print("   console.log('群聊下拉菜单:', document.getElementById('analysis-group'));")
    print()
    print("5. 检查下拉菜单选项数量:")
    print("   const talkerSelect = document.getElementById('talker-select');")
    print("   console.log('联系人选项数量:', talkerSelect.options.length);")
    print("   ")
    print("   const groupSelect = document.getElementById('analysis-group');")
    print("   console.log('群聊选项数量:', groupSelect.options.length);")

def main():
    """主函数"""
    print("🔧 下拉菜单修复测试")
    print("=" * 60)
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试API数据
    test_api_data()
    
    # 提供前端测试指南
    test_javascript_console()
    
    print("\n📋 修复内容总结:")
    print("1. ✅ 修复了API响应数据字段映射问题 (contacts -> data)")
    print("2. ✅ 修复了联系人字段名称 (wxid -> userName, displayName -> nickName)")
    print("3. ✅ 修复了群聊字段名称 (wxid -> name, displayName -> nickName)")
    print("4. ✅ 添加了联系人按昵称排序功能")
    print("5. ✅ 添加了群聊按成员数量排序功能")
    print("6. ✅ 添加了群聊搜索功能")
    print("7. ✅ 改进了显示格式 (显示备注、成员数量等)")
    print()
    print("🌐 请访问 http://127.0.0.1:3000 查看修复效果")
    print("📱 建议切换到'聊天记录'和'AI分析'标签页测试下拉菜单")

if __name__ == "__main__":
    main()
