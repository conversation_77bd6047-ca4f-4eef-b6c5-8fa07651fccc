#!/usr/bin/env python3
"""
快速功能测试脚本
"""
import subprocess
import json
import time
from datetime import datetime

def run_curl_test(url, description):
    """使用curl进行测试"""
    try:
        cmd = ['curl', '-s', '-w', '%{http_code}', url]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            # 分离响应内容和状态码
            output = result.stdout
            if len(output) >= 3:
                status_code = output[-3:]
                content = output[:-3]
                
                if status_code == '200':
                    print(f"✅ {description}: 成功 (200)")
                    return True, content
                else:
                    print(f"❌ {description}: HTTP {status_code}")
                    return False, None
            else:
                print(f"❌ {description}: 响应格式异常")
                return False, None
        else:
            print(f"❌ {description}: curl命令失败")
            return False, None
            
    except Exception as e:
        print(f"❌ {description}: 异常 - {e}")
        return False, None

def test_json_api(url, description):
    """测试JSON API"""
    try:
        cmd = ['curl', '-s', url]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            try:
                data = json.loads(result.stdout)
                if isinstance(data, dict):
                    if data.get('success') or data.get('status') == 'ok':
                        print(f"✅ {description}: API响应正常")
                        return True, data
                    else:
                        print(f"⚠️ {description}: API返回错误 - {data.get('message', '未知错误')}")
                        return False, data
                else:
                    print(f"✅ {description}: 返回数据格式正确")
                    return True, data
            except json.JSONDecodeError:
                print(f"❌ {description}: 响应不是有效JSON")
                return False, None
        else:
            print(f"❌ {description}: curl命令失败")
            return False, None
            
    except Exception as e:
        print(f"❌ {description}: 异常 - {e}")
        return False, None

def main():
    """主测试函数"""
    print("🚀 Flask聊天记录查询与AI分析系统 - 快速功能测试")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:3000"
    
    # 测试项目列表
    tests = [
        # 基础页面测试
        (base_url, "主页加载", run_curl_test),
        (f"{base_url}/health", "健康检查API", test_json_api),
        
        # Chatlog API测试
        (f"{base_url}/api/chatlog/status", "Chatlog服务状态", test_json_api),
        (f"{base_url}/api/chatlog/contacts", "联系人API", test_json_api),
        (f"{base_url}/api/chatlog/chatrooms", "群聊API", test_json_api),
        (f"{base_url}/api/chatlog/sessions", "会话API", test_json_api),
        
        # 静态资源测试
        (f"{base_url}/static/css/style.css", "CSS样式文件", run_curl_test),
        (f"{base_url}/static/js/main.js", "JavaScript文件", run_curl_test),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for url, description, test_func in tests:
        success, data = test_func(url, description)
        if success:
            passed += 1
        time.sleep(0.5)  # 避免请求过于频繁
    
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {(passed/total)*100:.1f}%")
    
    # 如果基础测试通过，进行更详细的功能测试
    if passed >= total * 0.7:  # 70%以上测试通过
        print("\n🎉 基础测试通过，进行详细功能验证...")
        
        # 测试页面内容
        print("\n📄 页面内容验证:")
        success, content = run_curl_test(base_url, "获取主页内容")
        if success and content:
            if "聊天记录查询与AI分析系统" in content:
                print("✅ 页面标题正确")
            if "overview-tab" in content and "chatlog-tab" in content:
                print("✅ 标签元素存在")
            if "ai-analysis-tab" in content and "scheduled-tab" in content:
                print("✅ AI分析和定时任务标签存在")
        
        # 测试API数据
        print("\n🔌 API数据验证:")
        success, data = test_json_api(f"{base_url}/api/chatlog/status", "Chatlog连接状态")
        if success and data:
            if data.get('success'):
                print("✅ Chatlog服务连接正常")
                conn_data = data.get('data', {})
                if 'contacts_count' in conn_data:
                    print(f"✅ 联系人数量: {conn_data['contacts_count']}")
        
        success, data = test_json_api(f"{base_url}/api/chatlog/contacts", "联系人数据")
        if success and data:
            contacts = data.get('data', [])
            print(f"✅ 获取到 {len(contacts)} 个联系人")
        
        success, data = test_json_api(f"{base_url}/api/chatlog/chatrooms", "群聊数据")
        if success and data:
            chatrooms = data.get('data', [])
            print(f"✅ 获取到 {len(chatrooms)} 个群聊")
    
    print(f"\n📋 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成简单的测试报告
    report = {
        'timestamp': datetime.now().isoformat(),
        'total_tests': total,
        'passed_tests': passed,
        'failed_tests': total - passed,
        'success_rate': (passed/total)*100,
        'status': 'PASS' if passed >= total * 0.8 else 'PARTIAL' if passed >= total * 0.5 else 'FAIL'
    }
    
    with open('test-results/quick_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 测试报告已保存: test-results/quick_test_report.json")
    
    return report['status'] == 'PASS'

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
