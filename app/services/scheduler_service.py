"""
定时任务调度服务
"""
import logging
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.executors.pool import ThreadPoolExecutor
from flask import current_app
from app.models import ScheduledTask, AnalysisItem, db
from app.services.ai_service import AIService
from app.services.chatlog_service import ChatlogService

class SchedulerService:
    """定时任务调度服务"""

    def __init__(self):
        self.scheduler = None
        self.logger = logging.getLogger(__name__)

    def init_scheduler(self, app):
        """初始化调度器"""
        if self.scheduler is None:
            # 配置执行器
            executors = {
                'default': ThreadPoolExecutor(max_workers=3)
            }

            # 配置作业存储
            job_defaults = {
                'coalesce': False,
                'max_instances': 1,
                'misfire_grace_time': 300  # 5分钟的容错时间
            }

            self.scheduler = BackgroundScheduler(
                executors=executors,
                job_defaults=job_defaults,
                timezone='Asia/Shanghai'
            )

            # 在应用上下文中启动调度器
            with app.app_context():
                self.scheduler.start()
                self.logger.info("定时任务调度器已启动")

                # 加载现有的定时任务
                self.load_scheduled_tasks()

    def load_scheduled_tasks(self):
        """加载数据库中的定时任务"""
        try:
            tasks = ScheduledTask.query.filter_by(enabled=True).all()

            for task in tasks:
                self.add_job(task)

            self.logger.info(f"已加载 {len(tasks)} 个定时任务")

        except Exception as e:
            self.logger.error(f"加载定时任务失败: {str(e)}")

    def add_job(self, task: ScheduledTask):
        """添加定时任务"""
        try:
            # 创建Cron触发器
            trigger = CronTrigger.from_crontab(task.cron_time)

            # 添加作业
            job = self.scheduler.add_job(
                func=self.execute_scheduled_analysis,
                trigger=trigger,
                args=[task.id],
                id=f"scheduled_task_{task.id}",
                name=task.name,
                replace_existing=True
            )

            # 更新下次运行时间
            task.next_run = job.next_run_time
            db.session.commit()

            self.logger.info(f"已添加定时任务: {task.name}")

        except Exception as e:
            self.logger.error(f"添加定时任务失败: {str(e)}")
            raise

    def remove_job(self, task_id: int):
        """移除定时任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            if self.scheduler.get_job(job_id):
                self.scheduler.remove_job(job_id)
                self.logger.info(f"已移除定时任务: {job_id}")
        except Exception as e:
            self.logger.error(f"移除定时任务失败: {str(e)}")

    def execute_scheduled_analysis(self, task_id: int):
        """执行定时分析任务"""
        try:
            # 获取任务配置
            task = ScheduledTask.query.get(task_id)
            if not task or not task.enabled:
                self.logger.warning(f"定时任务 {task_id} 不存在或已禁用")
                return

            self.logger.info(f"开始执行定时任务: {task.name}")

            # 更新最后运行时间
            task.last_run = datetime.utcnow()
            db.session.commit()

            # 获取分析项
            analysis_items = task.analysis_items.filter_by(enabled=True).all()

            if not analysis_items:
                self.logger.warning(f"定时任务 {task.name} 没有启用的分析项")
                return

            # 执行每个分析项
            for item in analysis_items:
                try:
                    self.execute_analysis_item(item)
                except Exception as e:
                    self.logger.error(f"执行分析项 {item.name} 失败: {str(e)}")
                    continue

            self.logger.info(f"定时任务 {task.name} 执行完成")

        except Exception as e:
            self.logger.error(f"执行定时任务失败: {str(e)}")

    def execute_analysis_item(self, item: AnalysisItem):
        """执行单个分析项"""
        try:
            # 获取昨天的聊天记录
            yesterday = datetime.now() - timedelta(days=1)
            time_range = yesterday.strftime('%Y-%m-%d') + '~' + yesterday.strftime('%Y-%m-%d')

            # 获取聊天记录
            chatlog_service = ChatlogService()
            chatlog_result = chatlog_service.get_chatlog(
                time_range=time_range,
                talker=item.group_name,
                limit=current_app.config.get('MAX_MESSAGE_COUNT', 10000)
            )

            if not chatlog_result.get('success'):
                self.logger.warning(f"获取群聊 {item.group_name} 的聊天记录失败")
                return

            messages = chatlog_result.get('data', [])

            if not messages:
                self.logger.info(f"群聊 {item.group_name} 昨天没有聊天记录")
                return

            # 执行AI分析
            ai_service = AIService()
            analysis_result = ai_service.analyze_chat(
                messages=messages,
                analysis_type=item.analysis_type,
                custom_prompt=item.custom_prompt
            )

            if not analysis_result.get('success'):
                self.logger.error(f"AI分析失败: {analysis_result.get('error')}")
                return

            # 保存分析结果
            from app.api.ai_analysis import generate_html_report
            import os
            import uuid
            from app.models import AnalysisHistory

            history_id = f"scheduled_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
            title = f"{item.group_name} - {item.analysis_type}分析 (定时任务)"

            # 生成HTML报告
            html_content = generate_html_report(
                title=title,
                group_name=item.group_name,
                analysis_type=item.analysis_type,
                time_range=time_range,
                message_count=len(messages),
                analysis_content=analysis_result['content'],
                model=analysis_result.get('model', 'unknown')
            )

            # 保存HTML文件
            reports_dir = current_app.config['ANALYSIS_REPORTS_DIR']
            if not os.path.exists(reports_dir):
                os.makedirs(reports_dir)

            file_path = os.path.join(reports_dir, f"{history_id}.html")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)

            # 保存到数据库
            history_record = AnalysisHistory(
                history_id=history_id,
                group_name=item.group_name,
                analysis_type=item.analysis_type,
                custom_prompt=item.custom_prompt,
                time_range=time_range,
                message_count=len(messages),
                title=title,
                file_path=file_path,
                status='completed'
            )

            db.session.add(history_record)
            db.session.commit()

            self.logger.info(f"定时分析项 {item.name} 执行成功，生成报告: {history_id}")

        except Exception as e:
            self.logger.error(f"执行分析项 {item.name} 失败: {str(e)}")
            raise

    def get_job_status(self, task_id: int):
        """获取任务状态"""
        job_id = f"scheduled_task_{task_id}"
        job = self.scheduler.get_job(job_id)

        if job:
            return {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            }
        return None

    def trigger_job(self, task_id: int):
        """手动触发任务"""
        try:
            job_id = f"scheduled_task_{task_id}"
            job = self.scheduler.get_job(job_id)

            if job:
                job.modify(next_run_time=datetime.now())
                self.logger.info(f"已手动触发任务: {job_id}")
                return True
            else:
                self.logger.warning(f"任务 {job_id} 不存在")
                return False

        except Exception as e:
            self.logger.error(f"手动触发任务失败: {str(e)}")
            return False

    def shutdown(self):
        """关闭调度器"""
        if self.scheduler and self.scheduler.running:
            self.scheduler.shutdown()
            self.logger.info("定时任务调度器已关闭")

# 全局调度器实例
scheduler_service = SchedulerService()