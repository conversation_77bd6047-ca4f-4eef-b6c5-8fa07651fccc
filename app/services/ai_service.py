"""
AI分析服务层
"""
import requests
import json
from flask import current_app
from typing import Dict, List, Optional, Any

class AIService:
    """AI服务基类"""

    def __init__(self):
        self.default_model = current_app.config['DEFAULT_AI_MODEL']

    def analyze_chat(self, messages: List[Dict], analysis_type: str,
                    custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        分析聊天记录

        Args:
            messages: 聊天消息列表
            analysis_type: 分析类型
            custom_prompt: 自定义提示词

        Returns:
            分析结果
        """
        # 根据默认模型选择服务
        if self.default_model.startswith('deepseek'):
            service = DeepSeekService()
        elif self.default_model.startswith('gemini'):
            service = GeminiService()
        else:
            raise ValueError(f"不支持的AI模型: {self.default_model}")

        return service.analyze_chat(messages, analysis_type, custom_prompt)

class DeepSeekService:
    """DeepSeek AI服务"""

    def __init__(self):
        self.api_key = current_app.config['DEEPSEEK_API_KEY']
        self.api_url = current_app.config['DEEPSEEK_API_URL']
        self.timeout = current_app.config.get('ANALYSIS_TIMEOUT', 300)

    def analyze_chat(self, messages: List[Dict], analysis_type: str,
                    custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """使用DeepSeek分析聊天记录"""

        if not self.api_key:
            raise ValueError("DeepSeek API Key未配置")

        # 构建提示词
        system_prompt = self._get_system_prompt(analysis_type)
        user_prompt = custom_prompt or self._get_default_prompt(analysis_type)

        # 格式化聊天记录
        chat_content = self._format_messages(messages)

        # 构建请求
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"{user_prompt}\n\n聊天记录:\n{chat_content}"}
            ],
            "temperature": 0.7,
            "max_tokens": 4000
        }

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        try:
            response = requests.post(
                self.api_url,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()

            result = response.json()

            return {
                'success': True,
                'content': result['choices'][0]['message']['content'],
                'model': 'deepseek-chat',
                'usage': result.get('usage', {})
            }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'DeepSeek API请求失败: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'DeepSeek分析失败: {str(e)}'
            }

    def _get_system_prompt(self, analysis_type: str) -> str:
        """获取系统提示词"""
        prompts = {
            'programming': '你是一个专业的编程技术分析师，擅长分析技术讨论、代码分享和问题解决过程。',
            'science': '你是一个科学学习内容分析专家，擅长总结学习讨论要点和知识分享。',
            'reading': '你是一个阅读讨论分析专家，擅长分析读书分享内容和观点交流。',
            'general': '你是一个聊天内容分析专家，擅长分析聊天话题、活跃度和情感倾向。'
        }
        return prompts.get(analysis_type, prompts['general'])

    def _get_default_prompt(self, analysis_type: str) -> str:
        """获取默认分析提示词"""
        prompts = {
            'programming': '''请分析这些编程技术讨论内容，包括：
1. 主要讨论的技术话题和问题
2. 代码分享和解决方案
3. 技术难点和学习重点
4. 参与者的技术水平和贡献
5. 有价值的技术资源和链接
请以结构化的方式总结分析结果。''',

            'science': '''请分析这些科学学习讨论内容，包括：
1. 主要学习话题和知识点
2. 学习方法和经验分享
3. 疑难问题和解答过程
4. 学习资源推荐
5. 学习进度和成果
请以结构化的方式总结分析结果。''',

            'reading': '''请分析这些阅读讨论内容，包括：
1. 讨论的书籍和文章
2. 主要观点和见解
3. 读书心得和感悟
4. 推荐的阅读材料
5. 讨论的深度和质量
请以结构化的方式总结分析结果。''',

            'general': '''请分析这些聊天记录，包括：
1. 主要话题和讨论内容
2. 参与者活跃度分析
3. 情感倾向和氛围
4. 有价值的信息和观点
5. 聊天质量和互动情况
请以结构化的方式总结分析结果。'''
        }
        return prompts.get(analysis_type, prompts['general'])

    def _format_messages(self, messages: List[Dict]) -> str:
        """格式化聊天消息"""
        formatted = []
        for msg in messages:
            timestamp = msg.get('timestamp', '')
            sender = msg.get('talker', '未知用户')
            content = msg.get('content', '')

            if content.strip():
                formatted.append(f"[{timestamp}] {sender}: {content}")

        return '\n'.join(formatted)

class GeminiService:
    """Google Gemini AI服务"""

    def __init__(self):
        self.api_key = current_app.config['GEMINI_API_KEY']
        self.api_url = current_app.config['GEMINI_API_URL']
        self.timeout = current_app.config.get('ANALYSIS_TIMEOUT', 300)

    def analyze_chat(self, messages: List[Dict], analysis_type: str,
                    custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """使用Gemini分析聊天记录"""

        if not self.api_key:
            raise ValueError("Gemini API Key未配置")

        # 构建提示词
        system_prompt = self._get_system_prompt(analysis_type)
        user_prompt = custom_prompt or self._get_default_prompt(analysis_type)

        # 格式化聊天记录
        chat_content = self._format_messages(messages)

        # 构建请求
        full_prompt = f"{system_prompt}\n\n{user_prompt}\n\n聊天记录:\n{chat_content}"

        payload = {
            "contents": [{
                "parts": [{"text": full_prompt}]
            }],
            "generationConfig": {
                "temperature": 0.7,
                "maxOutputTokens": 4000
            }
        }

        url = f"{self.api_url}/gemini-pro:generateContent?key={self.api_key}"

        try:
            response = requests.post(
                url,
                json=payload,
                timeout=self.timeout
            )
            response.raise_for_status()

            result = response.json()

            if 'candidates' in result and result['candidates']:
                content = result['candidates'][0]['content']['parts'][0]['text']
                return {
                    'success': True,
                    'content': content,
                    'model': 'gemini-pro',
                    'usage': result.get('usageMetadata', {})
                }
            else:
                return {
                    'success': False,
                    'error': 'Gemini返回空结果'
                }

        except requests.exceptions.RequestException as e:
            return {
                'success': False,
                'error': f'Gemini API请求失败: {str(e)}'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Gemini分析失败: {str(e)}'
            }

    def _get_system_prompt(self, analysis_type: str) -> str:
        """获取系统提示词"""
        prompts = {
            'programming': '你是一个专业的编程技术分析师，擅长分析技术讨论、代码分享和问题解决过程。',
            'science': '你是一个科学学习内容分析专家，擅长总结学习讨论要点和知识分享。',
            'reading': '你是一个阅读讨论分析专家，擅长分析读书分享内容和观点交流。',
            'general': '你是一个聊天内容分析专家，擅长分析聊天话题、活跃度和情感倾向。'
        }
        return prompts.get(analysis_type, prompts['general'])

    def _get_default_prompt(self, analysis_type: str) -> str:
        """获取默认分析提示词"""
        prompts = {
            'programming': '''请分析这些编程技术讨论内容，包括：
1. 主要讨论的技术话题和问题
2. 代码分享和解决方案
3. 技术难点和学习重点
4. 参与者的技术水平和贡献
5. 有价值的技术资源和链接
请以结构化的方式总结分析结果。''',

            'science': '''请分析这些科学学习讨论内容，包括：
1. 主要学习话题和知识点
2. 学习方法和经验分享
3. 疑难问题和解答过程
4. 学习资源推荐
5. 学习进度和成果
请以结构化的方式总结分析结果。''',

            'reading': '''请分析这些阅读讨论内容，包括：
1. 讨论的书籍和文章
2. 主要观点和见解
3. 读书心得和感悟
4. 推荐的阅读材料
5. 讨论的深度和质量
请以结构化的方式总结分析结果。''',

            'general': '''请分析这些聊天记录，包括：
1. 主要话题和讨论内容
2. 参与者活跃度分析
3. 情感倾向和氛围
4. 有价值的信息和观点
5. 聊天质量和互动情况
请以结构化的方式总结分析结果。'''
        }
        return prompts.get(analysis_type, prompts['general'])

    def _format_messages(self, messages: List[Dict]) -> str:
        """格式化聊天消息"""
        formatted = []
        for msg in messages:
            timestamp = msg.get('timestamp', '')
            sender = msg.get('talker', '未知用户')
            content = msg.get('content', '')

            if content.strip():
                formatted.append(f"[{timestamp}] {sender}: {content}")

        return '\n'.join(formatted)