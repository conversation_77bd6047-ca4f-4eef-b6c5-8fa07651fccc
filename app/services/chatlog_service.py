"""
Chatlog服务层
"""
import requests
from flask import current_app
from typing import Dict, List, Optional, Any

class ChatlogService:
    """Chatlog API服务类"""

    def __init__(self):
        self.base_url = current_app.config['CHATLOG_API_URL']
        self.timeout = current_app.config['CHATLOG_API_TIMEOUT']

    def _make_request(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """发起HTTP请求"""
        url = f"{self.base_url}{endpoint}"

        try:
            response = requests.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.Timeout:
            raise requests.RequestException("请求超时")
        except requests.exceptions.ConnectionError:
            raise requests.RequestException("无法连接到Chatlog服务")
        except requests.exceptions.HTTPError as e:
            raise requests.RequestException(f"HTTP错误: {e.response.status_code}")

    def get_chatlog(self, time_range: str, talker: Optional[str] = None,
                   limit: Optional[int] = None, offset: int = 0,
                   format_type: str = 'json') -> Dict[str, Any]:
        """
        获取聊天记录

        Args:
            time_range: 时间范围 (YYYY-MM-DD~YYYY-MM-DD)
            talker: 联系人/群聊ID
            limit: 返回记录数量限制
            offset: 分页偏移量
            format_type: 返回格式

        Returns:
            聊天记录数据
        """
        params = {
            'time': time_range,
            'offset': offset,
            'format': format_type
        }

        if talker:
            params['talker'] = talker
        if limit:
            params['limit'] = limit

        result = self._make_request('/api/v1/chatlog', params)

        # 格式化返回数据
        if result.get('success'):
            return {
                'success': True,
                'data': result.get('data', []),
                'pagination': {
                    'total': result.get('total', 0),
                    'page': (offset // limit) + 1 if limit else 1,
                    'pageSize': limit or len(result.get('data', [])),
                    'hasNext': result.get('hasNext', False)
                }
            }
        else:
            return result

    def get_contacts(self) -> Dict[str, Any]:
        """获取联系人列表"""
        return self._make_request('/api/v1/contact', {'format': 'json'})

    def get_chatrooms(self) -> Dict[str, Any]:
        """获取群聊列表"""
        return self._make_request('/api/v1/chatroom', {'format': 'json'})

    def get_sessions(self) -> Dict[str, Any]:
        """获取会话列表"""
        return self._make_request('/api/v1/session', {'format': 'json'})

    def get_media(self, msgid: str) -> Any:
        """
        获取多媒体内容

        Args:
            msgid: 消息ID

        Returns:
            媒体文件内容或信息
        """
        params = {'msgid': msgid}

        try:
            url = f"{self.base_url}/api/v1/media"
            response = requests.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()

            # 如果是文件内容，返回文件数据
            if response.headers.get('content-type', '').startswith('application/'):
                return response.content, response.headers.get('content-type')

            # 否则返回JSON数据
            return response.json()

        except requests.exceptions.RequestException as e:
            raise requests.RequestException(f"获取媒体文件失败: {str(e)}")

    def test_connection(self) -> Dict[str, Any]:
        """测试连接状态"""
        try:
            # 测试会话接口来验证连接
            result = self._make_request('/api/v1/session', {'format': 'json', 'limit': 1})
            return {
                'success': True,
                'message': '连接正常',
                'data': {'status': 'connected', 'sessions_count': len(result.get('data', []))}
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'连接失败: {str(e)}'
            }