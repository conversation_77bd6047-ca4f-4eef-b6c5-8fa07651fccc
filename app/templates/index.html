<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-comments"></i> {{ title }}</h1>
            <div class="version">v{{ version }}</div>
        </header>

        <main class="main-content">
            <!-- 导航标签 -->
            <div class="nav-tabs">
                <button class="nav-tab active" data-tab="overview">概览</button>
                <button class="nav-tab" data-tab="chatlog">聊天记录</button>
                <button class="nav-tab" data-tab="analysis">AI分析</button>
                <button class="nav-tab" data-tab="scheduled">定时任务</button>
                <button class="nav-tab" data-tab="history">分析历史</button>
            </div>

            <!-- 概览页面 -->
            <div class="tab-content active" id="overview-tab">
                <div class="welcome-section">
                    <h2>欢迎使用聊天记录查询与AI分析系统</h2>
                    <p>基于Flask框架重构的现代化聊天记录分析平台</p>

                    <div class="feature-grid">
                        <div class="feature-card">
                            <i class="fas fa-search"></i>
                            <h3>聊天记录查询</h3>
                            <p>快速查询和筛选聊天记录</p>
                        </div>

                        <div class="feature-card">
                            <i class="fas fa-brain"></i>
                            <h3>AI智能分析</h3>
                            <p>使用AI技术分析聊天内容</p>
                        </div>

                        <div class="feature-card">
                            <i class="fas fa-clock"></i>
                            <h3>定时任务</h3>
                            <p>自动化定时分析任务</p>
                        </div>

                        <div class="feature-card">
                            <i class="fas fa-history"></i>
                            <h3>历史管理</h3>
                            <p>分析历史记录管理</p>
                        </div>
                    </div>
                </div>

                <div class="status-section">
                    <div class="status-card">
                        <h3>系统状态</h3>
                        <div class="status-item">
                            <span class="status-label">Flask应用:</span>
                            <span class="status-value running">运行中</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Chatlog服务:</span>
                            <span class="status-value" id="chatlog-status">检查中...</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聊天记录页面 -->
            <div class="tab-content" id="chatlog-tab">
                <div class="section-card">
                    <h3><i class="fas fa-search"></i> 聊天记录查询</h3>

                    <form class="query-form" id="chatlog-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>时间范围</label>
                                <select id="time-range">
                                    <option value="today">今天</option>
                                    <option value="yesterday">昨天</option>
                                    <option value="week">最近一周</option>
                                    <option value="month">最近一月</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>查询类型</label>
                                <select id="talker-type-select">
                                    <option value="">全部</option>
                                    <option value="chatroom">群聊</option>
                                    <option value="contact">联系人</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>具体选择</label>
                                <select id="talker-select" disabled>
                                    <option value="">请先选择查询类型</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>记录数量</label>
                                <select id="limit-select">
                                    <option value="100">100条</option>
                                    <option value="500">500条</option>
                                    <option value="1000">1000条</option>
                                    <option value="">不限制</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row" id="custom-date-row" style="display: none;">
                            <div class="form-group">
                                <label>开始日期</label>
                                <input type="date" id="start-date">
                            </div>
                            <div class="form-group">
                                <label>结束日期</label>
                                <input type="date" id="end-date">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 查询
                        </button>
                    </form>

                    <div class="results-section" id="chatlog-results">
                        <!-- 查询结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- AI分析页面 -->
            <div class="tab-content" id="analysis-tab">
                <div class="section-card">
                    <h3><i class="fas fa-brain"></i> AI智能分析</h3>

                    <form class="analysis-form" id="analysis-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label>群聊名称</label>
                                <select id="analysis-group" required>
                                    <option value="">请选择群聊</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label>分析类型</label>
                                <select id="analysis-type">
                                    <option value="general">通用分析</option>
                                    <option value="programming">编程技术讨论</option>
                                    <option value="science">科学学习内容</option>
                                    <option value="reading">阅读讨论</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label>时间范围</label>
                            <select id="analysis-time-range">
                                <option value="">全部时间</option>
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="week">最近一周</option>
                                <option value="month">最近一月</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>自定义提示词 (可选)</label>
                            <textarea id="custom-prompt" placeholder="输入自定义分析要求..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-magic"></i> 开始分析
                        </button>
                    </form>

                    <div class="analysis-progress" id="analysis-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p>AI分析进行中，请稍候...</p>
                    </div>

                    <div class="analysis-result" id="analysis-result">
                        <!-- 分析结果将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 定时任务页面 -->
            <div class="tab-content" id="scheduled-tab">
                <div class="section-card">
                    <h3><i class="fas fa-clock"></i> 定时任务管理</h3>

                    <div class="task-controls">
                        <button class="btn btn-primary" id="add-task-btn">
                            <i class="fas fa-plus"></i> 新增任务
                        </button>
                        <button class="btn btn-secondary" id="refresh-tasks-btn">
                            <i class="fas fa-sync"></i> 刷新状态
                        </button>
                    </div>

                    <div class="tasks-list" id="tasks-list">
                        <!-- 定时任务列表将在这里显示 -->
                    </div>
                </div>
            </div>

            <!-- 分析历史页面 -->
            <div class="tab-content" id="history-tab">
                <div class="section-card">
                    <h3><i class="fas fa-history"></i> 分析历史</h3>

                    <div class="history-controls">
                        <button class="btn btn-secondary" id="refresh-history-btn">
                            <i class="fas fa-sync"></i> 刷新
                        </button>
                    </div>

                    <div class="history-list" id="history-list">
                        <!-- 历史记录列表将在这里显示 -->
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>