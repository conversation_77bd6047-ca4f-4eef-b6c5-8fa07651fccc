<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-comments"></i> {{ title }}</h1>
            <div class="version">v{{ version }}</div>
        </header>

        <main class="main-content">
            <div class="welcome-section">
                <h2>欢迎使用聊天记录查询与AI分析系统</h2>
                <p>基于Flask框架重构的现代化聊天记录分析平台</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-search"></i>
                        <h3>聊天记录查询</h3>
                        <p>快速查询和筛选聊天记录</p>
                    </div>

                    <div class="feature-card">
                        <i class="fas fa-brain"></i>
                        <h3>AI智能分析</h3>
                        <p>使用AI技术分析聊天内容</p>
                    </div>

                    <div class="feature-card">
                        <i class="fas fa-clock"></i>
                        <h3>定时任务</h3>
                        <p>自动化定时分析任务</p>
                    </div>

                    <div class="feature-card">
                        <i class="fas fa-history"></i>
                        <h3>历史管理</h3>
                        <p>分析历史记录管理</p>
                    </div>
                </div>
            </div>

            <div class="status-section">
                <div class="status-card">
                    <h3>系统状态</h3>
                    <div class="status-item">
                        <span class="status-label">Flask应用:</span>
                        <span class="status-value running">运行中</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">Chatlog服务:</span>
                        <span class="status-value" id="chatlog-status">检查中...</span>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>