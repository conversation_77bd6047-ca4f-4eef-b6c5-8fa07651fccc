"""
Flask应用工厂模式
"""
import os
import logging
from logging.handlers import RotatingFileHandler
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_cors import CORS
from config import config

# 初始化扩展
db = SQLAlchemy()
cors = CORS()

def create_app(config_name=None):
    """
    应用工厂函数

    Args:
        config_name: 配置名称 ('development', 'production', 'testing')

    Returns:
        Flask应用实例
    """
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')

    app = Flask(__name__)

    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # 初始化扩展
    db.init_app(app)
    cors.init_app(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })

    # 注册蓝图
    from app.api import chatlog_bp, ai_analysis_bp, scheduled_tasks_bp
    app.register_blueprint(chatlog_bp, url_prefix='/api')
    app.register_blueprint(ai_analysis_bp, url_prefix='/api')
    app.register_blueprint(scheduled_tasks_bp, url_prefix='/api')

    # 注册主页路由
    from app.main import main_bp
    app.register_blueprint(main_bp)

    # 配置日志
    if not app.debug and not app.testing:
        setup_logging(app)

    # 创建数据库表
    with app.app_context():
        db.create_all()

    return app

def setup_logging(app):
    """配置日志系统"""
    if not os.path.exists('logs'):
        os.mkdir('logs')

    file_handler = RotatingFileHandler(
        app.config['LOG_FILE'],
        maxBytes=10240000,
        backupCount=10
    )

    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))

    file_handler.setLevel(getattr(logging, app.config['LOG_LEVEL']))
    app.logger.addHandler(file_handler)
    app.logger.setLevel(getattr(logging, app.config['LOG_LEVEL']))
    app.logger.info('Flask应用启动')