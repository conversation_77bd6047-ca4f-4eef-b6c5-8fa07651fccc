"""
统一错误处理模块
"""
import logging
import traceback
from functools import wraps
from flask import jsonify, current_app, request
from werkzeug.exceptions import HTTPException

class APIError(Exception):
    """API错误基类"""

    def __init__(self, message, error_code=None, status_code=500, details=None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or 'INTERNAL_ERROR'
        self.status_code = status_code
        self.details = details or {}

class ValidationError(APIError):
    """参数验证错误"""

    def __init__(self, message, details=None):
        super().__init__(
            message=message,
            error_code='VALIDATION_ERROR',
            status_code=400,
            details=details
        )

class NotFoundError(APIError):
    """资源不存在错误"""

    def __init__(self, message="资源不存在"):
        super().__init__(
            message=message,
            error_code='NOT_FOUND',
            status_code=404
        )

class ServiceUnavailableError(APIError):
    """服务不可用错误"""

    def __init__(self, message="服务暂时不可用"):
        super().__init__(
            message=message,
            error_code='SERVICE_UNAVAILABLE',
            status_code=503
        )

class AuthenticationError(APIError):
    """认证错误"""

    def __init__(self, message="认证失败"):
        super().__init__(
            message=message,
            error_code='AUTHENTICATION_ERROR',
            status_code=401
        )

def register_error_handlers(app):
    """注册错误处理器"""

    @app.errorhandler(APIError)
    def handle_api_error(error):
        """处理API错误"""
        current_app.logger.error(f"API错误: {error.message}", extra={
            'error_code': error.error_code,
            'status_code': error.status_code,
            'details': error.details,
            'url': request.url,
            'method': request.method
        })

        return jsonify({
            'success': False,
            'error': error.error_code,
            'message': error.message,
            'details': error.details
        }), error.status_code

    @app.errorhandler(ValidationError)
    def handle_validation_error(error):
        """处理验证错误"""
        return handle_api_error(error)

    @app.errorhandler(NotFoundError)
    def handle_not_found_error(error):
        """处理404错误"""
        return handle_api_error(error)

    @app.errorhandler(ServiceUnavailableError)
    def handle_service_unavailable_error(error):
        """处理服务不可用错误"""
        return handle_api_error(error)

    @app.errorhandler(AuthenticationError)
    def handle_authentication_error(error):
        """处理认证错误"""
        return handle_api_error(error)

    @app.errorhandler(404)
    def handle_404(error):
        """处理404错误"""
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '接口不存在'
            }), 404
        return error

    @app.errorhandler(405)
    def handle_405(error):
        """处理方法不允许错误"""
        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': 'METHOD_NOT_ALLOWED',
                'message': '请求方法不允许'
            }), 405
        return error

    @app.errorhandler(500)
    def handle_500(error):
        """处理服务器内部错误"""
        current_app.logger.error(f"服务器内部错误: {str(error)}", extra={
            'url': request.url,
            'method': request.method,
            'traceback': traceback.format_exc()
        })

        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': 'INTERNAL_ERROR',
                'message': '服务器内部错误'
            }), 500
        return error

    @app.errorhandler(Exception)
    def handle_unexpected_error(error):
        """处理未预期的错误"""
        current_app.logger.error(f"未预期的错误: {str(error)}", extra={
            'url': request.url,
            'method': request.method,
            'traceback': traceback.format_exc()
        })

        if request.path.startswith('/api/'):
            return jsonify({
                'success': False,
                'error': 'UNEXPECTED_ERROR',
                'message': '发生未预期的错误'
            }), 500
        return error