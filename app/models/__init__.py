"""
数据库模型
"""
from datetime import datetime
from app import db

class AnalysisHistory(db.Model):
    """分析历史记录模型"""
    __tablename__ = 'analysis_history'

    id = db.Column(db.Integer, primary_key=True)
    history_id = db.Column(db.String(100), unique=True, nullable=False, index=True)
    group_name = db.Column(db.String(200), nullable=False)
    analysis_type = db.Column(db.String(50), nullable=False)
    custom_prompt = db.Column(db.Text)
    time_range = db.Column(db.String(50))
    message_count = db.Column(db.Integer, default=0)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    title = db.Column(db.String(300), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    status = db.Column(db.String(20), default='completed')  # completed, failed, processing

    def to_dict(self):
        """转换为字典格式"""
        return {
            'historyId': self.history_id,
            'groupName': self.group_name,
            'analysisType': self.analysis_type,
            'customPrompt': self.custom_prompt,
            'timeRange': self.time_range,
            'messageCount': self.message_count,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'title': self.title,
            'filePath': self.file_path,
            'status': self.status
        }

class ScheduledTask(db.Model):
    """定时任务配置模型"""
    __tablename__ = 'scheduled_tasks'

    id = db.Column(db.Integer, primary_key=True)
    enabled = db.Column(db.Boolean, default=True)
    cron_time = db.Column(db.String(50), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)

    # 关联分析项
    analysis_items = db.relationship('AnalysisItem', backref='scheduled_task', lazy='dynamic', cascade='all, delete-orphan')

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'enabled': self.enabled,
            'cronTime': self.cron_time,
            'name': self.name,
            'description': self.description,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'lastRun': self.last_run.isoformat() if self.last_run else None,
            'nextRun': self.next_run.isoformat() if self.next_run else None,
            'analysisItems': [item.to_dict() for item in self.analysis_items]
        }

class AnalysisItem(db.Model):
    """分析项模型"""
    __tablename__ = 'analysis_items'

    id = db.Column(db.Integer, primary_key=True)
    item_id = db.Column(db.String(100), unique=True, nullable=False, index=True)
    name = db.Column(db.String(200), nullable=False)
    group_name = db.Column(db.String(200), nullable=False)
    analysis_type = db.Column(db.String(50), nullable=False)
    custom_prompt = db.Column(db.Text)
    enabled = db.Column(db.Boolean, default=True)

    # 外键关联到定时任务
    scheduled_task_id = db.Column(db.Integer, db.ForeignKey('scheduled_tasks.id'))

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.item_id,
            'name': self.name,
            'groupName': self.group_name,
            'analysisType': self.analysis_type,
            'customPrompt': self.custom_prompt,
            'enabled': self.enabled
        }