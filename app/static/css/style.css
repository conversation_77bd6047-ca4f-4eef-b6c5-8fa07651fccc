/* 基础样式 - 苹果风格设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header h1 i {
    color: #007aff;
}

.version {
    background: #007aff;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    gap: 24px;
}

.welcome-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.welcome-section h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
}

.welcome-section p {
    color: #86868b;
    font-size: 16px;
    margin-bottom: 32px;
}

/* 功能网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 32px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 32px;
    color: #007aff;
    margin-bottom: 16px;
}

.feature-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
}

.feature-card p {
    color: #86868b;
    font-size: 14px;
}

/* 状态区域 */
.status-section {
    display: grid;
    gap: 20px;
}

.status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
    color: #1d1d1f;
}

.status-value {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-value.running {
    background: #34c759;
    color: white;
}

.status-value.error {
    background: #ff3b30;
    color: white;
}

.status-value.warning {
    background: #ff9500;
    color: white;
}