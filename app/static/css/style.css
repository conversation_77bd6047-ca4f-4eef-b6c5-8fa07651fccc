/* 基础样式 - 苹果风格设计 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #1d1d1f;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header h1 i {
    color: #007aff;
}

.version {
    background: #007aff;
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

/* 主要内容区域 */
.main-content {
    display: grid;
    gap: 24px;
}

.welcome-section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.welcome-section h2 {
    font-size: 24px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
}

.welcome-section p {
    color: #86868b;
    font-size: 16px;
    margin-bottom: 32px;
}

/* 功能网格 */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 32px;
}

.feature-card {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-card i {
    font-size: 32px;
    color: #007aff;
    margin-bottom: 16px;
}

.feature-card h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 8px;
}

.feature-card p {
    color: #86868b;
    font-size: 14px;
}

/* 状态区域 */
.status-section {
    display: grid;
    gap: 20px;
}

.status-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
    color: #1d1d1f;
}

.status-value {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.status-value.running {
    background: #34c759;
    color: white;
}

.status-value.error {
    background: #ff3b30;
    color: white;
}

.status-value.warning {
    background: #ff9500;
    color: white;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 8px;
    margin-bottom: 24px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    gap: 4px;
}

.nav-tab {
    flex: 1;
    padding: 12px 20px;
    border: none;
    background: transparent;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    color: #86868b;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: rgba(0, 122, 255, 0.1);
    color: #007aff;
}

.nav-tab.active {
    background: #007aff;
    color: white;
    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

/* 标签内容 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 区域卡片 */
.section-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.section-card h3 {
    font-size: 20px;
    font-weight: 600;
    color: #1d1d1f;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-card h3 i {
    color: #007aff;
}

/* 表单样式 */
.query-form, .analysis-form {
    margin-bottom: 32px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: #1d1d1f;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group select,
.form-group input,
.form-group textarea {
    padding: 12px 16px;
    border: 1px solid #d1d1d6;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
}

.form-group select:focus,
.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* 按钮样式 */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
}

.btn-primary {
    background: #007aff;
    color: white;
}

.btn-primary:hover {
    background: #0056cc;
    transform: translateY(-1px);
    box-shadow: 0 4px 16px rgba(0, 122, 255, 0.3);
}

.btn-secondary {
    background: #f2f2f7;
    color: #1d1d1f;
}

.btn-secondary:hover {
    background: #e5e5ea;
}

.btn-danger {
    background: #ff3b30;
    color: white;
}

.btn-danger:hover {
    background: #d70015;
}

/* 进度条 */
.analysis-progress {
    text-align: center;
    padding: 32px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f2f2f7;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 16px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007aff, #5ac8fa);
    border-radius: 4px;
    animation: progress-animation 2s ease-in-out infinite;
}

@keyframes progress-animation {
    0% { width: 0%; }
    50% { width: 70%; }
    100% { width: 100%; }
}

/* 结果区域 */
.results-section,
.analysis-result,
.tasks-list,
.history-list {
    margin-top: 24px;
}

.result-item,
.task-item,
.history-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 16px;
    border-left: 4px solid #007aff;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.item-title {
    font-weight: 600;
    color: #1d1d1f;
}

.item-meta {
    font-size: 12px;
    color: #86868b;
}

.item-actions {
    display: flex;
    gap: 8px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
}

/* 控制按钮组 */
.task-controls,
.history-controls {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    color: #86868b;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* 响应式设计更新 */
@media (max-width: 768px) {
    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        min-width: calc(50% - 2px);
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .item-actions {
        width: 100%;
        justify-content: flex-end;
    }
}