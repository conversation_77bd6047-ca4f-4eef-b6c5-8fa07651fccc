/**
 * 主应用JavaScript文件
 */

// 应用初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('Flask聊天记录查询与AI分析系统已加载');

    // 初始化标签切换
    initTabSwitching();

    // 初始化表单
    initForms();

    // 检查Chatlog服务状态
    checkChatlogStatus();

    // 加载初始数据
    loadInitialData();

    // 设置定时检查
    setInterval(checkChatlogStatus, 30000); // 每30秒检查一次
});

/**
 * 检查Chatlog服务状态
 */
async function checkChatlogStatus() {
    const statusElement = document.getElementById('chatlog-status');

    try {
        const response = await fetch('/api/chatlog/status');
        const data = await response.json();

        if (data.success) {
            statusElement.textContent = '连接正常';
            statusElement.className = 'status-value running';
        } else {
            statusElement.textContent = '连接失败';
            statusElement.className = 'status-value error';
        }
    } catch (error) {
        console.error('检查Chatlog状态失败:', error);
        statusElement.textContent = '连接异常';
        statusElement.className = 'status-value error';
    }
}

/**
 * 通用API请求函数
 */
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
        },
    };

    const config = { ...defaultOptions, ...options };

    try {
        const response = await fetch(url, config);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.message || `HTTP error! status: ${response.status}`);
        }

        return data;
    } catch (error) {
        console.error('API请求失败:', error);
        throw error;
    }
}

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

/**
 * 格式化时间
 */
function formatTime(timestamp) {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 初始化标签切换
 */
function initTabSwitching() {
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    navTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTab = tab.dataset.tab;

            // 移除所有活动状态
            navTabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // 激活当前标签
            tab.classList.add('active');
            document.getElementById(`${targetTab}-tab`).classList.add('active');

            // 加载标签内容
            loadTabContent(targetTab);
        });
    });
}

/**
 * 初始化表单
 */
function initForms() {
    // 时间范围选择
    const timeRangeSelect = document.getElementById('time-range');
    const customDateRow = document.getElementById('custom-date-row');

    if (timeRangeSelect) {
        timeRangeSelect.addEventListener('change', (e) => {
            if (e.target.value === 'custom') {
                customDateRow.style.display = 'block';
            } else {
                customDateRow.style.display = 'none';
            }
        });
    }

    // 聊天记录查询表单
    const chatlogForm = document.getElementById('chatlog-form');
    if (chatlogForm) {
        chatlogForm.addEventListener('submit', handleChatlogQuery);
    }

    // AI分析表单
    const analysisForm = document.getElementById('analysis-form');
    if (analysisForm) {
        analysisForm.addEventListener('submit', handleAnalysisSubmit);
    }

    // 查询类型选择
    const talkerTypeSelect = document.getElementById('talker-type-select');
    if (talkerTypeSelect) {
        talkerTypeSelect.addEventListener('change', handleTalkerTypeChange);
    }
}

/**
 * 处理查询类型变化
 */
async function handleTalkerTypeChange(event) {
    const talkerType = event.target.value;
    const talkerSelect = document.getElementById('talker-select');

    if (!talkerSelect) return;

    // 重置第二个下拉菜单
    talkerSelect.innerHTML = '<option value="">加载中...</option>';
    talkerSelect.disabled = true;

    console.log(`🔄 查询类型变更为: ${talkerType}`);

    try {
        if (talkerType === 'chatroom') {
            // 加载群聊列表
            await loadChatroomsForSelect();
        } else if (talkerType === 'contact') {
            // 加载联系人列表
            await loadContactsForSelect();
        } else {
            // 全部选项
            talkerSelect.innerHTML = '<option value="">全部</option>';
            talkerSelect.disabled = false;
        }
    } catch (error) {
        console.error('❌ 加载数据失败:', error);
        talkerSelect.innerHTML = '<option value="">加载失败</option>';
        talkerSelect.disabled = false;
    }
}

/**
 * 为选择器加载群聊列表
 */
async function loadChatroomsForSelect() {
    console.log('👥 正在加载群聊列表...');

    const response = await apiRequest('/api/chatlog/chatrooms');
    const talkerSelect = document.getElementById('talker-select');

    if (response.success) {
        const chatrooms = response.data || [];
        console.log(`👥 成功获取 ${chatrooms.length} 个群聊`);

        // 过滤和排序群聊：按成员数量从高到低排序
        const filteredChatrooms = chatrooms
            .filter(chatroom => chatroom.name && chatroom.nickName)
            .sort((a, b) => {
                const aUserCount = (a.users && Array.isArray(a.users)) ? a.users.length : 0;
                const bUserCount = (b.users && Array.isArray(b.users)) ? b.users.length : 0;
                return bUserCount - aUserCount;
            });

        // 填充下拉菜单
        talkerSelect.innerHTML = '<option value="">请选择群聊</option>';

        filteredChatrooms.forEach(chatroom => {
            const option = document.createElement('option');
            option.value = chatroom.name;
            const memberCount = (chatroom.users && Array.isArray(chatroom.users)) ? chatroom.users.length : 0;
            option.textContent = `${chatroom.nickName} (${memberCount}人)`;
            talkerSelect.appendChild(option);
        });

        talkerSelect.disabled = false;
        console.log(`✅ 已加载 ${filteredChatrooms.length} 个群聊到下拉菜单`);
    } else {
        console.error('👥 群聊API失败:', response.message);
        talkerSelect.innerHTML = '<option value="">加载失败</option>';
        talkerSelect.disabled = false;
    }
}

/**
 * 为选择器加载联系人列表
 */
async function loadContactsForSelect() {
    console.log('📋 正在加载联系人列表...');

    const response = await apiRequest('/api/chatlog/contacts');
    const talkerSelect = document.getElementById('talker-select');

    if (response.success) {
        const contacts = response.data || [];
        console.log(`📋 成功获取 ${contacts.length} 个联系人`);

        // 过滤和排序联系人：按昵称排序
        const filteredContacts = contacts
            .filter(contact => contact.userName && contact.nickName)
            .sort((a, b) => (a.nickName || '').localeCompare(b.nickName || ''));

        // 填充下拉菜单
        talkerSelect.innerHTML = '<option value="">请选择联系人</option>';

        filteredContacts.forEach(contact => {
            const option = document.createElement('option');
            option.value = contact.userName;
            const displayName = contact.remark ?
                `${contact.nickName} (${contact.remark})` :
                contact.nickName;
            option.textContent = displayName;
            talkerSelect.appendChild(option);
        });

        talkerSelect.disabled = false;
        console.log(`✅ 已加载 ${filteredContacts.length} 个联系人到下拉菜单`);
    } else {
        console.error('📋 联系人API失败:', response.message);
        talkerSelect.innerHTML = '<option value="">加载失败</option>';
        talkerSelect.disabled = false;
    }
}

/**
 * 加载初始数据
 */
async function loadInitialData() {
    console.log('🚀 开始加载初始数据...');
    try {
        // 只为AI分析页面加载群聊列表
        await loadGroupsForAnalysis();
        console.log('✅ 初始数据加载完成');
    } catch (error) {
        console.error('❌ 加载初始数据失败:', error);
    }
}

/**
 * 为AI分析页面加载群聊列表
 */
async function loadGroupsForAnalysis() {
    console.log('🤖 为AI分析页面加载群聊数据...');
    try {
        const chatroomsResponse = await apiRequest('/api/chatlog/chatrooms');
        console.log('👥 群聊API响应:', chatroomsResponse);

        if (chatroomsResponse.success) {
            const chatrooms = chatroomsResponse.data || [];
            console.log(`👥 成功获取 ${chatrooms.length} 个群聊`);
            populateGroupSelect(chatrooms);
        } else {
            console.error('👥 群聊API失败:', chatroomsResponse.message);
        }
    } catch (error) {
        console.error('❌ 加载AI分析群聊失败:', error);
    }
}

/**
 * 加载联系人和群聊列表 (保留用于向后兼容)
 */
async function loadContactsAndGroups() {
    console.log('📞 开始加载联系人和群聊数据...');
    try {
        // 加载联系人
        console.log('📋 正在加载联系人...');
        const contactsResponse = await apiRequest('/api/chatlog/contacts');
        console.log('📋 联系人API响应:', contactsResponse);

        if (contactsResponse.success) {
            const contacts = contactsResponse.data || [];
            console.log(`📋 成功获取 ${contacts.length} 个联系人`);
            populateContactSelect(contacts);
        } else {
            console.error('📋 联系人API失败:', contactsResponse.message);
        }

        // 加载群聊
        console.log('👥 正在加载群聊...');
        const chatroomsResponse = await apiRequest('/api/chatlog/chatrooms');
        console.log('👥 群聊API响应:', chatroomsResponse);

        if (chatroomsResponse.success) {
            const chatrooms = chatroomsResponse.data || [];
            console.log(`👥 成功获取 ${chatrooms.length} 个群聊`);
            populateGroupSelect(chatrooms);
        } else {
            console.error('👥 群聊API失败:', chatroomsResponse.message);
        }
    } catch (error) {
        console.error('❌ 加载联系人和群聊失败:', error);
    }
}

/**
 * 填充联系人选择框
 */
function populateContactSelect(contacts) {
    const talkerSelect = document.getElementById('talker-select');
    if (!talkerSelect) return;

    // 清空现有选项
    talkerSelect.innerHTML = '<option value="">全部</option>';

    // 过滤和排序联系人
    const filteredContacts = contacts
        .filter(contact => contact.userName && contact.nickName) // 过滤掉无效数据
        .sort((a, b) => (a.nickName || '').localeCompare(b.nickName || '')); // 按昵称排序

    filteredContacts.forEach(contact => {
        const option = document.createElement('option');
        option.value = contact.userName; // 使用userName作为值
        // 显示格式：昵称 (备注) 或者 昵称
        const displayName = contact.remark ?
            `${contact.nickName} (${contact.remark})` :
            contact.nickName;
        option.textContent = displayName;
        talkerSelect.appendChild(option);
    });

    console.log(`已加载 ${filteredContacts.length} 个联系人到下拉菜单`);
}

/**
 * 填充群聊选择框
 */
function populateGroupSelect(chatrooms) {
    const analysisGroupSelect = document.getElementById('analysis-group');
    if (!analysisGroupSelect) return;

    // 清空现有选项并添加搜索框
    analysisGroupSelect.innerHTML = '<option value="">请选择群聊</option>';

    // 过滤和排序群聊：按成员数量从高到低排序
    const filteredChatrooms = chatrooms
        .filter(chatroom => chatroom.name && chatroom.nickName) // 过滤掉无效数据
        .sort((a, b) => {
            const aUserCount = (a.users && Array.isArray(a.users)) ? a.users.length : 0;
            const bUserCount = (b.users && Array.isArray(b.users)) ? b.users.length : 0;
            return bUserCount - aUserCount; // 降序排列
        });

    filteredChatrooms.forEach(chatroom => {
        const option = document.createElement('option');
        option.value = chatroom.name; // 使用name作为值

        // 显示格式：群聊名称 (成员数)
        const memberCount = (chatroom.users && Array.isArray(chatroom.users)) ? chatroom.users.length : 0;
        const displayName = `${chatroom.nickName} (${memberCount}人)`;
        option.textContent = displayName;

        // 添加数据属性用于搜索
        option.setAttribute('data-search', chatroom.nickName.toLowerCase());

        analysisGroupSelect.appendChild(option);
    });

    console.log(`已加载 ${filteredChatrooms.length} 个群聊到下拉菜单`);

    // 添加群聊搜索功能
    addGroupSearchFunctionality(analysisGroupSelect, filteredChatrooms);
}

/**
 * 添加群聊搜索功能
 */
function addGroupSearchFunctionality(selectElement, chatrooms) {
    // 创建搜索输入框
    const searchContainer = document.createElement('div');
    searchContainer.className = 'group-search-container';
    searchContainer.style.cssText = `
        position: relative;
        margin-bottom: 8px;
    `;

    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = '搜索群聊名称...';
    searchInput.className = 'group-search-input';
    searchInput.style.cssText = `
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 14px;
        box-sizing: border-box;
    `;

    searchContainer.appendChild(searchInput);

    // 将搜索框插入到select元素之前
    selectElement.parentNode.insertBefore(searchContainer, selectElement);

    // 搜索功能
    searchInput.addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();

        // 重新填充选项
        selectElement.innerHTML = '<option value="">请选择群聊</option>';

        const filteredResults = chatrooms.filter(chatroom =>
            chatroom.nickName.toLowerCase().includes(searchTerm)
        );

        filteredResults.forEach(chatroom => {
            const option = document.createElement('option');
            option.value = chatroom.name;
            const memberCount = (chatroom.users && Array.isArray(chatroom.users)) ? chatroom.users.length : 0;
            option.textContent = `${chatroom.nickName} (${memberCount}人)`;
            selectElement.appendChild(option);
        });

        // 显示搜索结果数量
        if (searchTerm && filteredResults.length === 0) {
            const noResultOption = document.createElement('option');
            noResultOption.value = '';
            noResultOption.textContent = '未找到匹配的群聊';
            noResultOption.disabled = true;
            selectElement.appendChild(noResultOption);
        }
    });
}

/**
 * 加载标签内容
 */
function loadTabContent(tabName) {
    switch (tabName) {
        case 'overview':
            // 概览页面已经加载
            break;
        case 'chatlog':
            // 聊天记录页面
            break;
        case 'analysis':
            // AI分析页面
            break;
        case 'scheduled':
            loadScheduledTasks();
            break;
        case 'history':
            loadAnalysisHistory();
            break;
    }
}

/**
 * 处理聊天记录查询
 */
async function handleChatlogQuery(event) {
    event.preventDefault();

    const timeRange = document.getElementById('time-range').value;
    const talker = document.getElementById('talker-select').value;
    const limit = document.getElementById('limit-select').value;

    // 构建时间范围
    let timeParam = '';
    if (timeRange === 'custom') {
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        if (startDate && endDate) {
            timeParam = `${startDate}~${endDate}`;
        }
    } else {
        timeParam = getTimeRangeParam(timeRange);
    }

    if (!timeParam) {
        showNotification('请选择时间范围', 'error');
        return;
    }

    try {
        const params = new URLSearchParams({
            time: timeParam,
            talker: talker || 'all'  // 如果没有选择talker，使用'all'
        });

        if (limit) params.append('limit', limit);

        const response = await apiRequest(`/api/chatlog/chatlog?${params}`);

        if (response.success) {
            displayChatlogResults(response.data, response.pagination);
            showNotification('查询成功', 'success');
        } else {
            showNotification(response.message || '查询失败', 'error');
        }
    } catch (error) {
        console.error('查询聊天记录失败:', error);
        showNotification('查询失败', 'error');
    }
}

/**
 * 获取时间范围参数
 */
function getTimeRangeParam(range) {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

    const formatDate = (date) => date.toISOString().split('T')[0];

    switch (range) {
        case 'today':
            return formatDate(today) + '~' + formatDate(today);
        case 'yesterday':
            return formatDate(yesterday) + '~' + formatDate(yesterday);
        case 'week':
            const weekAgo = new Date(today);
            weekAgo.setDate(today.getDate() - 7);
            return formatDate(weekAgo) + '~' + formatDate(today);
        case 'month':
            const monthAgo = new Date(today);
            monthAgo.setMonth(today.getMonth() - 1);
            return formatDate(monthAgo) + '~' + formatDate(today);
        default:
            return '';
    }
}

/**
 * 显示聊天记录查询结果
 */
function displayChatlogResults(data, pagination) {
    const resultsContainer = document.getElementById('chatlog-results');
    if (!resultsContainer) return;

    if (!data || data.length === 0) {
        resultsContainer.innerHTML = '<div class="no-results">没有找到符合条件的聊天记录</div>';
        return;
    }

    let html = '<div class="results-header">';
    html += `<h3>查询结果 (${data.length} 条记录)</h3>`;
    html += '</div>';

    html += '<div class="results-list">';
    data.forEach(message => {
        html += '<div class="message-item">';
        html += `<div class="message-time">${formatTime(message.timestamp * 1000)}</div>`;
        html += `<div class="message-sender">${message.talker || '未知'}</div>`;
        html += `<div class="message-content">${escapeHtml(message.content || '')}</div>`;
        html += '</div>';
    });
    html += '</div>';

    resultsContainer.innerHTML = html;
}

/**
 * 转义HTML字符
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 处理AI分析提交
 */
async function handleAnalysisSubmit(event) {
    event.preventDefault();

    const groupSelect = document.getElementById('analysis-group');
    const analysisType = document.getElementById('analysis-type');

    if (!groupSelect.value) {
        showNotification('请选择要分析的群聊', 'error');
        return;
    }

    try {
        const response = await apiRequest('/api/ai/analyze', {
            method: 'POST',
            body: JSON.stringify({
                group_id: groupSelect.value,
                analysis_type: analysisType.value
            })
        });

        if (response.success) {
            showNotification('分析任务已提交', 'success');
        } else {
            showNotification(response.message || '提交失败', 'error');
        }
    } catch (error) {
        console.error('提交分析任务失败:', error);
        showNotification('提交失败', 'error');
    }
}

/**
 * 加载定时任务
 */
async function loadScheduledTasks() {
    try {
        const response = await apiRequest('/api/scheduled/tasks');
        if (response.success) {
            displayScheduledTasks(response.data);
        }
    } catch (error) {
        console.error('加载定时任务失败:', error);
    }
}

/**
 * 显示定时任务
 */
function displayScheduledTasks(tasks) {
    const container = document.getElementById('scheduled-tasks-list');
    if (!container) return;

    if (!tasks || tasks.length === 0) {
        container.innerHTML = '<div class="no-results">暂无定时任务</div>';
        return;
    }

    let html = '';
    tasks.forEach(task => {
        html += '<div class="task-item">';
        html += `<div class="task-name">${task.name}</div>`;
        html += `<div class="task-status">${task.status}</div>`;
        html += `<div class="task-next-run">${formatTime(task.next_run)}</div>`;
        html += '</div>';
    });

    container.innerHTML = html;
}

/**
 * 加载分析历史
 */
async function loadAnalysisHistory() {
    try {
        const response = await apiRequest('/api/ai/history');
        if (response.success) {
            displayAnalysisHistory(response.data);
        }
    } catch (error) {
        console.error('加载分析历史失败:', error);
    }
}

/**
 * 显示分析历史
 */
function displayAnalysisHistory(history) {
    const container = document.getElementById('analysis-history-list');
    if (!container) return;

    if (!history || history.length === 0) {
        container.innerHTML = '<div class="no-results">暂无分析历史</div>';
        return;
    }

    let html = '';
    history.forEach(item => {
        html += '<div class="history-item">';
        html += `<div class="history-title">${item.title}</div>`;
        html += `<div class="history-time">${formatTime(item.created_at)}</div>`;
        html += `<div class="history-status">${item.status}</div>`;
        html += '</div>';
    });

    container.innerHTML = html;
}

// 导出函数供其他模块使用
window.ChatlogApp = {
    apiRequest,
    showNotification,
    formatTime,
    formatFileSize,
    checkChatlogStatus,
    loadScheduledTasks,
    loadAnalysisHistory,
    displayChatlogResults,
    handleAnalysisSubmit,
    loadContactsAndGroups,
    populateContactSelect,
    populateGroupSelect,
    handleTalkerTypeChange,
    loadChatroomsForSelect,
    loadContactsForSelect,
    loadGroupsForAnalysis
};