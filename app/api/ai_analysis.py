"""
AI分析API
"""
import os
import uuid
from datetime import datetime
from flask import request, jsonify, current_app
from app.api import ai_analysis_bp
from app.services.ai_service import AIService
from app.services.chatlog_service import ChatlogService
from app.models import AnalysisHistory, db

@ai_analysis_bp.route('/ai-analysis', methods=['POST'])
def create_analysis():
    """执行AI分析"""
    try:
        data = request.get_json()

        # 验证必需参数
        group_name = data.get('groupName')
        analysis_type = data.get('analysisType', 'general')
        custom_prompt = data.get('customPrompt')
        time_range = data.get('timeRange')

        if not group_name:
            return jsonify({
                'success': False,
                'error': 'INVALID_PARAMETER',
                'message': '群聊名称不能为空'
            }), 400

        # 获取聊天记录
        chatlog_service = ChatlogService()

        # 构建查询参数
        query_params = {}
        if time_range:
            query_params['time_range'] = time_range

        # 这里需要根据群聊名称获取群聊ID
        # 暂时使用群聊名称作为talker参数
        query_params['talker'] = group_name
        query_params['limit'] = current_app.config.get('MAX_MESSAGE_COUNT', 10000)

        chatlog_result = chatlog_service.get_chatlog(**query_params)

        if not chatlog_result.get('success'):
            return jsonify({
                'success': False,
                'error': 'CHATLOG_ERROR',
                'message': '获取聊天记录失败'
            }), 503

        messages = chatlog_result.get('data', [])

        if not messages:
            return jsonify({
                'success': False,
                'error': 'NO_DATA',
                'message': '未找到聊天记录'
            }), 404

        # 执行AI分析
        ai_service = AIService()
        analysis_result = ai_service.analyze_chat(messages, analysis_type, custom_prompt)

        if not analysis_result.get('success'):
            return jsonify({
                'success': False,
                'error': 'AI_ANALYSIS_ERROR',
                'message': analysis_result.get('error', 'AI分析失败')
            }), 500

        # 生成分析报告
        history_id = f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        title = f"{group_name} - {analysis_type}分析"

        # 生成HTML报告
        html_content = generate_html_report(
            title=title,
            group_name=group_name,
            analysis_type=analysis_type,
            time_range=time_range,
            message_count=len(messages),
            analysis_content=analysis_result['content'],
            model=analysis_result.get('model', 'unknown')
        )

        # 保存HTML文件
        reports_dir = current_app.config['ANALYSIS_REPORTS_DIR']
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        file_path = os.path.join(reports_dir, f"{history_id}.html")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 保存到数据库
        history_record = AnalysisHistory(
            history_id=history_id,
            group_name=group_name,
            analysis_type=analysis_type,
            custom_prompt=custom_prompt,
            time_range=time_range,
            message_count=len(messages),
            title=title,
            file_path=file_path,
            status='completed'
        )

        db.session.add(history_record)
        db.session.commit()

        return jsonify({
            'success': True,
            'historyId': history_id,
            'title': title,
            'metadata': history_record.to_dict()
        })

    except Exception as e:
        current_app.logger.error(f'AI分析失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@ai_analysis_bp.route('/analysis-history', methods=['GET'])
def get_analysis_history():
    """获取分析历史记录"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        # 分页查询
        pagination = AnalysisHistory.query.order_by(
            AnalysisHistory.timestamp.desc()
        ).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return jsonify({
            'success': True,
            'data': [record.to_dict() for record in pagination.items],
            'pagination': {
                'page': page,
                'pages': pagination.pages,
                'per_page': per_page,
                'total': pagination.total,
                'has_next': pagination.has_next,
                'has_prev': pagination.has_prev
            }
        })

    except Exception as e:
        current_app.logger.error(f'获取分析历史失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@ai_analysis_bp.route('/analysis-history/<history_id>', methods=['GET'])
def get_analysis_detail(history_id):
    """获取特定分析记录详情"""
    try:
        record = AnalysisHistory.query.filter_by(history_id=history_id).first()

        if not record:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '分析记录不存在'
            }), 404

        return jsonify({
            'success': True,
            'data': record.to_dict()
        })

    except Exception as e:
        current_app.logger.error(f'获取分析详情失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@ai_analysis_bp.route('/analysis-history/<history_id>', methods=['DELETE'])
def delete_analysis(history_id):
    """删除分析记录"""
    try:
        record = AnalysisHistory.query.filter_by(history_id=history_id).first()

        if not record:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '分析记录不存在'
            }), 404

        # 删除HTML文件
        if os.path.exists(record.file_path):
            os.remove(record.file_path)

        # 删除数据库记录
        db.session.delete(record)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '分析记录已删除'
        })

    except Exception as e:
        current_app.logger.error(f'删除分析记录失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

def generate_html_report(title, group_name, analysis_type, time_range,
                        message_count, analysis_content, model):
    """生成HTML分析报告"""

    html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f7fa;
        }}

        .report-container {{
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }}

        .report-header {{
            border-bottom: 2px solid #007aff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}

        .report-title {{
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0 0 10px 0;
        }}

        .report-meta {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }}

        .meta-item {{
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 8px;
            border-left: 4px solid #007aff;
        }}

        .meta-label {{
            font-weight: 600;
            color: #666;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}

        .meta-value {{
            font-size: 16px;
            color: #1d1d1f;
            margin-top: 4px;
        }}

        .report-content {{
            margin-top: 30px;
        }}

        .content-section {{
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 20px;
        }}

        .section-title {{
            font-size: 20px;
            font-weight: 600;
            color: #1d1d1f;
            margin: 0 0 16px 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }}

        .analysis-content {{
            white-space: pre-wrap;
            line-height: 1.8;
            font-size: 16px;
        }}

        .footer {{
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e5e7;
            text-align: center;
            color: #86868b;
            font-size: 14px;
        }}

        @media (max-width: 768px) {{
            body {{
                padding: 16px;
            }}

            .report-container {{
                padding: 20px;
            }}

            .report-meta {{
                grid-template-columns: 1fr;
            }}
        }}
    </style>
</head>
<body>
    <div class="report-container">
        <div class="report-header">
            <h1 class="report-title">{title}</h1>
            <div class="report-meta">
                <div class="meta-item">
                    <div class="meta-label">群聊名称</div>
                    <div class="meta-value">{group_name}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">分析类型</div>
                    <div class="meta-value">{analysis_type}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">时间范围</div>
                    <div class="meta-value">{time_range or '全部'}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">消息数量</div>
                    <div class="meta-value">{message_count:,} 条</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">AI模型</div>
                    <div class="meta-value">{model}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">生成时间</div>
                    <div class="meta-value">{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</div>
                </div>
            </div>
        </div>

        <div class="report-content">
            <div class="content-section">
                <h2 class="section-title">
                    <span>🤖</span>
                    AI分析结果
                </h2>
                <div class="analysis-content">{analysis_content}</div>
            </div>
        </div>

        <div class="footer">
            <p>本报告由 Flask聊天记录查询与AI分析系统 自动生成</p>
        </div>
    </div>
</body>
</html>
    """

    return html_template

@ai_analysis_bp.route('/batch-analysis', methods=['POST'])
def create_batch_analysis():
    """执行批量AI分析"""
    try:
        data = request.get_json()

        # 验证必需参数
        analysis_items = data.get('analysisItems', [])
        time_range = data.get('timeRange')

        if not analysis_items:
            return jsonify({
                'success': False,
                'error': 'INVALID_PARAMETER',
                'message': '分析项不能为空'
            }), 400

        # 创建批量分析任务
        batch_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"

        # 启动后台批量分析任务
        from threading import Thread
        thread = Thread(target=execute_batch_analysis, args=(batch_id, analysis_items, time_range))
        thread.daemon = True
        thread.start()

        return jsonify({
            'success': True,
            'batchId': batch_id,
            'message': '批量分析任务已启动'
        })

    except Exception as e:
        current_app.logger.error(f'创建批量分析任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@ai_analysis_bp.route('/batch-analysis/<batch_id>/status', methods=['GET'])
def get_batch_analysis_status(batch_id):
    """获取批量分析状态"""
    try:
        # 从缓存或数据库获取批量分析状态
        status = get_batch_status(batch_id)

        if not status:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '批量分析任务不存在'
            }), 404

        return jsonify({
            'success': True,
            'data': status
        })

    except Exception as e:
        current_app.logger.error(f'获取批量分析状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@ai_analysis_bp.route('/batch-analysis/<batch_id>/cancel', methods=['POST'])
def cancel_batch_analysis(batch_id):
    """取消批量分析"""
    try:
        # 设置取消标志
        success = cancel_batch_task(batch_id)

        if success:
            return jsonify({
                'success': True,
                'message': '批量分析任务已取消'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '批量分析任务不存在或已完成'
            }), 404

    except Exception as e:
        current_app.logger.error(f'取消批量分析失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

# 批量分析状态缓存
batch_status_cache = {}

def execute_batch_analysis(batch_id, analysis_items, time_range):
    """执行批量分析任务"""
    try:
        # 初始化状态
        batch_status_cache[batch_id] = {
            'batchId': batch_id,
            'status': 'running',
            'progress': 0,
            'total': len(analysis_items),
            'completed': 0,
            'failed': 0,
            'results': [],
            'startTime': datetime.now().isoformat(),
            'endTime': None,
            'cancelled': False
        }

        current_app.logger.info(f"开始执行批量分析任务: {batch_id}")

        for i, item in enumerate(analysis_items):
            # 检查是否被取消
            if batch_status_cache[batch_id]['cancelled']:
                batch_status_cache[batch_id]['status'] = 'cancelled'
                break

            try:
                # 执行单个分析
                result = execute_single_analysis(item, time_range)

                if result['success']:
                    batch_status_cache[batch_id]['completed'] += 1
                    batch_status_cache[batch_id]['results'].append(result)
                else:
                    batch_status_cache[batch_id]['failed'] += 1
                    batch_status_cache[batch_id]['results'].append({
                        'success': False,
                        'groupName': item.get('groupName'),
                        'error': result.get('error', '分析失败')
                    })

            except Exception as e:
                current_app.logger.error(f"批量分析项失败: {str(e)}")
                batch_status_cache[batch_id]['failed'] += 1
                batch_status_cache[batch_id]['results'].append({
                    'success': False,
                    'groupName': item.get('groupName'),
                    'error': str(e)
                })

            # 更新进度
            batch_status_cache[batch_id]['progress'] = int((i + 1) / len(analysis_items) * 100)

        # 完成批量分析
        if not batch_status_cache[batch_id]['cancelled']:
            batch_status_cache[batch_id]['status'] = 'completed'

        batch_status_cache[batch_id]['endTime'] = datetime.now().isoformat()

        current_app.logger.info(f"批量分析任务完成: {batch_id}")

    except Exception as e:
        current_app.logger.error(f"批量分析任务失败: {str(e)}")
        batch_status_cache[batch_id]['status'] = 'failed'
        batch_status_cache[batch_id]['error'] = str(e)
        batch_status_cache[batch_id]['endTime'] = datetime.now().isoformat()

def execute_single_analysis(item, time_range):
    """执行单个分析项"""
    try:
        group_name = item.get('groupName')
        analysis_type = item.get('analysisType', 'general')
        custom_prompt = item.get('customPrompt')

        # 获取聊天记录
        chatlog_service = ChatlogService()

        query_params = {
            'talker': group_name,
            'limit': current_app.config.get('MAX_MESSAGE_COUNT', 10000)
        }

        if time_range:
            query_params['time_range'] = time_range

        chatlog_result = chatlog_service.get_chatlog(**query_params)

        if not chatlog_result.get('success'):
            return {
                'success': False,
                'error': '获取聊天记录失败'
            }

        messages = chatlog_result.get('data', [])

        if not messages:
            return {
                'success': False,
                'error': '未找到聊天记录'
            }

        # 执行AI分析
        ai_service = AIService()
        analysis_result = ai_service.analyze_chat(messages, analysis_type, custom_prompt)

        if not analysis_result.get('success'):
            return {
                'success': False,
                'error': analysis_result.get('error', 'AI分析失败')
            }

        # 生成分析报告
        history_id = f"batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{str(uuid.uuid4())[:8]}"
        title = f"{group_name} - {analysis_type}分析 (批量分析)"

        # 生成HTML报告
        html_content = generate_html_report(
            title=title,
            group_name=group_name,
            analysis_type=analysis_type,
            time_range=time_range,
            message_count=len(messages),
            analysis_content=analysis_result['content'],
            model=analysis_result.get('model', 'unknown')
        )

        # 保存HTML文件
        reports_dir = current_app.config['ANALYSIS_REPORTS_DIR']
        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        file_path = os.path.join(reports_dir, f"{history_id}.html")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # 保存到数据库
        history_record = AnalysisHistory(
            history_id=history_id,
            group_name=group_name,
            analysis_type=analysis_type,
            custom_prompt=custom_prompt,
            time_range=time_range,
            message_count=len(messages),
            title=title,
            file_path=file_path,
            status='completed'
        )

        db.session.add(history_record)
        db.session.commit()

        return {
            'success': True,
            'historyId': history_id,
            'title': title,
            'groupName': group_name,
            'messageCount': len(messages)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def get_batch_status(batch_id):
    """获取批量分析状态"""
    return batch_status_cache.get(batch_id)

def cancel_batch_task(batch_id):
    """取消批量分析任务"""
    if batch_id in batch_status_cache:
        if batch_status_cache[batch_id]['status'] == 'running':
            batch_status_cache[batch_id]['cancelled'] = True
            return True
    return False