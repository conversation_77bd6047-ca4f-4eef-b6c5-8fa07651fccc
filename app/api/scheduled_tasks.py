"""
定时任务API
"""
import uuid
from flask import request, jsonify, current_app
from app.api import scheduled_tasks_bp
from app.models import ScheduledTask, AnalysisItem, db
from app.services.scheduler_service import scheduler_service

@scheduled_tasks_bp.route('/scheduled-analysis-status', methods=['GET'])
def get_scheduled_analysis_status():
    """获取定时任务状态"""
    try:
        tasks = ScheduledTask.query.all()

        status_list = []
        for task in tasks:
            task_dict = task.to_dict()

            # 获取调度器中的任务状态
            job_status = scheduler_service.get_job_status(task.id)
            if job_status:
                task_dict['jobStatus'] = job_status

            status_list.append(task_dict)

        return jsonify({
            'success': True,
            'data': status_list
        })

    except Exception as e:
        current_app.logger.error(f'获取定时任务状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@scheduled_tasks_bp.route('/scheduled-analysis-config', methods=['POST'])
def configure_scheduled_analysis():
    """配置定时任务"""
    try:
        data = request.get_json()

        # 验证必需参数
        enabled = data.get('enabled', True)
        cron_time = data.get('cronTime')
        name = data.get('name')
        description = data.get('description', '')
        analysis_items = data.get('analysisItems', [])

        if not cron_time:
            return jsonify({
                'success': False,
                'error': 'INVALID_PARAMETER',
                'message': 'Cron表达式不能为空'
            }), 400

        if not name:
            return jsonify({
                'success': False,
                'error': 'INVALID_PARAMETER',
                'message': '任务名称不能为空'
            }), 400

        # 验证Cron表达式
        try:
            from apscheduler.triggers.cron import CronTrigger
            CronTrigger.from_crontab(cron_time)
        except Exception as e:
            return jsonify({
                'success': False,
                'error': 'INVALID_CRON',
                'message': f'无效的Cron表达式: {str(e)}'
            }), 400

        # 创建或更新定时任务
        task_id = data.get('id')
        if task_id:
            # 更新现有任务
            task = ScheduledTask.query.get(task_id)
            if not task:
                return jsonify({
                    'success': False,
                    'error': 'NOT_FOUND',
                    'message': '定时任务不存在'
                }), 404
        else:
            # 创建新任务
            task = ScheduledTask()

        # 更新任务属性
        task.enabled = enabled
        task.cron_time = cron_time
        task.name = name
        task.description = description

        # 保存任务
        if not task_id:
            db.session.add(task)

        db.session.flush()  # 获取任务ID

        # 删除现有的分析项
        if task_id:
            AnalysisItem.query.filter_by(scheduled_task_id=task.id).delete()

        # 添加新的分析项
        for item_data in analysis_items:
            analysis_item = AnalysisItem(
                item_id=item_data.get('id') or str(uuid.uuid4()),
                name=item_data.get('name'),
                group_name=item_data.get('groupName'),
                analysis_type=item_data.get('analysisType', 'general'),
                custom_prompt=item_data.get('customPrompt'),
                enabled=item_data.get('enabled', True),
                scheduled_task_id=task.id
            )
            db.session.add(analysis_item)

        db.session.commit()

        # 更新调度器
        if enabled:
            scheduler_service.add_job(task)
        else:
            scheduler_service.remove_job(task.id)

        return jsonify({
            'success': True,
            'data': task.to_dict(),
            'message': '定时任务配置成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'配置定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@scheduled_tasks_bp.route('/trigger-scheduled-analysis', methods=['POST'])
def trigger_scheduled_analysis():
    """手动触发定时任务"""
    try:
        data = request.get_json()
        task_id = data.get('taskId')

        if not task_id:
            return jsonify({
                'success': False,
                'error': 'INVALID_PARAMETER',
                'message': '任务ID不能为空'
            }), 400

        # 检查任务是否存在
        task = ScheduledTask.query.get(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '定时任务不存在'
            }), 404

        # 手动触发任务
        success = scheduler_service.trigger_job(task_id)

        if success:
            return jsonify({
                'success': True,
                'message': '定时任务已手动触发'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'TRIGGER_FAILED',
                'message': '触发定时任务失败'
            }), 500

    except Exception as e:
        current_app.logger.error(f'手动触发定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@scheduled_tasks_bp.route('/scheduled-analysis-config/<int:task_id>', methods=['DELETE'])
def delete_scheduled_task(task_id):
    """删除定时任务"""
    try:
        task = ScheduledTask.query.get(task_id)
        if not task:
            return jsonify({
                'success': False,
                'error': 'NOT_FOUND',
                'message': '定时任务不存在'
            }), 404

        # 从调度器中移除任务
        scheduler_service.remove_job(task_id)

        # 删除数据库记录
        db.session.delete(task)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '定时任务已删除'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f'删除定时任务失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500