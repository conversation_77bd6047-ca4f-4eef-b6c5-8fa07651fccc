"""
聊天记录查询API
"""
import requests
from flask import request, jsonify, current_app
from app.api import chatlog_bp
from app.services.chatlog_service import ChatlogService

@chatlog_bp.route('/chatlog', methods=['GET'])
def get_chatlog():
    """获取聊天记录"""
    try:
        # 获取查询参数
        time_range = request.args.get('time')
        if not time_range:
            raise ValueError('time参数是必需的')

        talker = request.args.get('talker')
        limit = request.args.get('limit', type=int)
        offset = request.args.get('offset', type=int, default=0)
        format_type = request.args.get('format', default='json')

        # 调用服务层
        chatlog_service = ChatlogService()
        result = chatlog_service.get_chatlog(
            time_range=time_range,
            talker=talker,
            limit=limit,
            offset=offset,
            format_type=format_type
        )

        return jsonify(result)

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': 'INVALID_PARAMETER',
            'message': str(e)
        }), 400
    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'CHATLOG_SERVICE_ERROR',
            'message': '无法连接到Chatlog服务'
        }), 503
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        current_app.logger.error(f'获取聊天记录失败: {str(e)}\n{error_details}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': f'服务器内部错误: {str(e)}'
        }), 500

@chatlog_bp.route('/contacts', methods=['GET'])
def get_contacts():
    """获取联系人列表"""
    try:
        chatlog_service = ChatlogService()
        result = chatlog_service.get_contacts()
        return jsonify(result)

    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'CHATLOG_SERVICE_ERROR',
            'message': '无法连接到Chatlog服务'
        }), 503
    except Exception as e:
        current_app.logger.error(f'获取联系人列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@chatlog_bp.route('/chatrooms', methods=['GET'])
def get_chatrooms():
    """获取群聊列表"""
    try:
        chatlog_service = ChatlogService()
        result = chatlog_service.get_chatrooms()
        return jsonify(result)

    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'CHATLOG_SERVICE_ERROR',
            'message': '无法连接到Chatlog服务'
        }), 503
    except Exception as e:
        current_app.logger.error(f'获取群聊列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@chatlog_bp.route('/sessions', methods=['GET'])
def get_sessions():
    """获取会话列表"""
    try:
        chatlog_service = ChatlogService()
        result = chatlog_service.get_sessions()
        return jsonify(result)

    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'CHATLOG_SERVICE_ERROR',
            'message': '无法连接到Chatlog服务'
        }), 503
    except Exception as e:
        current_app.logger.error(f'获取会话列表失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@chatlog_bp.route('/media', methods=['GET'])
def get_media():
    """获取多媒体内容"""
    try:
        msgid = request.args.get('msgid')
        if not msgid:
            raise ValueError('msgid参数是必需的')

        chatlog_service = ChatlogService()
        result = chatlog_service.get_media(msgid)

        # 如果是文件内容，直接返回
        if isinstance(result, tuple):
            return result

        return jsonify(result)

    except ValueError as e:
        return jsonify({
            'success': False,
            'error': 'INVALID_PARAMETER',
            'message': str(e)
        }), 400
    except requests.RequestException as e:
        return jsonify({
            'success': False,
            'error': 'CHATLOG_SERVICE_ERROR',
            'message': '无法连接到Chatlog服务'
        }), 503
    except Exception as e:
        current_app.logger.error(f'获取多媒体内容失败: {str(e)}')
        return jsonify({
            'success': False,
            'error': 'INTERNAL_ERROR',
            'message': '服务器内部错误'
        }), 500

@chatlog_bp.route('/status', methods=['GET'])
def check_status():
    """检查Chatlog服务状态"""
    try:
        chatlog_service = ChatlogService()
        result = chatlog_service.test_connection()
        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f'检查Chatlog状态失败: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'连接失败: {str(e)}'
        }), 503