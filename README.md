# Flask聊天记录查询与AI分析系统

基于Flask框架重构的现代化聊天记录分析平台，提供聊天记录查询、AI智能分析、定时任务、历史管理等核心功能。

## 🚀 项目特性

- **现代化Web应用**: 基于Flask框架，采用Blueprint模式和应用工厂模式
- **AI智能分析**: 集成DeepSeek和Gemini AI模型，支持多种分析类型
- **定时任务系统**: 使用APScheduler实现灵活的Cron表达式调度
- **批量分析**: 支持多群聊同时分析，实时进度监控
- **历史管理**: 完整的分析历史记录存储和管理
- **现代化界面**: 苹果风格设计，响应式布局
- **错误处理**: 统一的错误处理机制和日志系统
- **测试覆盖**: 完整的单元测试和集成测试

## 🛠 技术栈

### 后端
- **Flask 2.3.3**: Web框架
- **SQLAlchemy**: ORM数据库操作
- **APScheduler**: 定时任务调度
- **Flask-CORS**: 跨域资源共享
- **python-dotenv**: 环境变量管理

### 前端
- **HTML5 + CSS3**: 现代化标记和样式
- **JavaScript (ES6+)**: 前端交互逻辑
- **Font Awesome**: 图标库
- **响应式设计**: 支持多设备访问

### AI服务
- **DeepSeek API**: 深度学习模型
- **Google Gemini API**: 谷歌AI模型
- **自定义提示词**: 灵活的分析配置

### 数据库
- **SQLite**: 默认数据库（开发环境）
- **PostgreSQL**: 生产环境支持

## 📦 安装部署

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd chatlogwebui

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境配置
复制 `.env` 文件并配置必要的环境变量：

```bash
cp .env.example .env
```

编辑 `.env` 文件：
```env
# Flask配置
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# 数据库配置
DATABASE_URL=sqlite:///chatlog.db

# Chatlog API配置
CHATLOG_API_URL=http://localhost:5030

# AI服务配置
DEEPSEEK_API_KEY=your-deepseek-api-key
GEMINI_API_KEY=your-gemini-api-key
DEFAULT_AI_MODEL=deepseek-chat

# 应用设置
APP_HOST=0.0.0.0
APP_PORT=3000
```

### 3. 启动应用
```bash
python run.py
```

访问 `http://localhost:3000` 查看应用。

## 🎯 功能模块

### 1. 聊天记录查询
- 支持时间范围筛选
- 联系人和群聊过滤
- 分页查询和结果展示
- 多媒体内容获取

### 2. AI智能分析
- **分析类型**:
  - 通用分析: 话题、活跃度、情感倾向
  - 编程技术讨论: 技术话题、代码分享、解决方案
  - 科学学习内容: 学习方法、知识点、资源推荐
  - 阅读讨论: 书籍推荐、观点交流、读书心得
- **自定义提示词**: 灵活配置分析要求
- **HTML报告生成**: 美观的分析结果展示

### 3. 定时任务系统
- Cron表达式配置
- 任务状态监控
- 手动触发执行
- 执行日志记录
- 多分析项支持

### 4. 批量分析
- 多群聊并发分析
- 实时进度监控
- 任务取消功能
- 结果统一管理

### 5. 分析历史管理
- 历史记录查询
- 报告文件管理
- 元数据存储
- 批量删除功能

## 🔧 API接口

### 聊天记录相关
- `GET /api/chatlog/chatlog` - 查询聊天记录
- `GET /api/chatlog/contacts` - 获取联系人列表
- `GET /api/chatlog/chatrooms` - 获取群聊列表
- `GET /api/chatlog/sessions` - 获取会话列表
- `GET /api/chatlog/media` - 获取多媒体内容
- `GET /api/chatlog/status` - 检查服务状态

### AI分析相关
- `POST /api/ai-analysis` - 执行AI分析
- `GET /api/analysis-history` - 获取分析历史
- `GET /api/analysis-history/<id>` - 获取分析详情
- `DELETE /api/analysis-history/<id>` - 删除分析记录
- `POST /api/batch-analysis` - 批量分析
- `GET /api/batch-analysis/<id>/status` - 批量分析状态

### 定时任务相关
- `GET /api/scheduled-analysis-status` - 获取任务状态
- `POST /api/scheduled-analysis-config` - 配置定时任务
- `POST /api/trigger-scheduled-analysis` - 手动触发任务
- `DELETE /api/scheduled-analysis-config/<id>` - 删除任务

## 🧪 测试

运行测试套件：
```bash
# 运行所有测试
python -m pytest

# 运行特定测试文件
python -m pytest tests/test_api.py -v

# 运行测试并显示覆盖率
python -m pytest --cov=app tests/
```

## 📁 项目结构

```
chatlogwebui/
├── app/                    # 应用主目录
│   ├── __init__.py        # 应用工厂
│   ├── models/            # 数据模型
│   ├── api/               # API蓝图
│   │   ├── chatlog.py     # 聊天记录API
│   │   ├── ai_analysis.py # AI分析API
│   │   └── scheduled_tasks.py # 定时任务API
│   ├── services/          # 业务逻辑层
│   │   ├── chatlog_service.py # 聊天记录服务
│   │   ├── ai_service.py      # AI分析服务
│   │   └── scheduler_service.py # 调度服务
│   ├── utils/             # 工具模块
│   │   └── error_handler.py # 错误处理
│   ├── templates/         # Jinja2模板
│   └── static/            # 静态资源
│       ├── css/           # 样式文件
│       ├── js/            # JavaScript文件
│       └── images/        # 图片资源
├── tests/                 # 测试文件
├── logs/                  # 日志文件
├── reports/               # 分析报告
├── uploads/               # 上传文件
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
├── run.py                 # 应用启动文件
├── .env                   # 环境变量
└── README.md              # 项目说明
```

## 🔒 安全考虑

- 环境变量管理敏感信息
- API密钥安全存储
- 请求参数验证
- 错误信息脱敏
- CORS配置
- SQL注入防护

## 🚀 生产部署

### 使用Gunicorn
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:3000 run:app
```

### 使用Docker
```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 3000

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:3000", "run:app"]
```

### 使用Nginx反向代理
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📝 开发日志

- ✅ 项目初始化与环境搭建
- ✅ 核心架构设计与配置
- ✅ 聊天记录查询API开发
- ✅ AI分析服务集成
- ✅ 定时任务系统开发
- ✅ 分析历史管理功能
- ✅ 前端界面开发
- ✅ 批量分析功能实现
- ✅ 错误处理与日志系统
- ✅ 测试与优化

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]

---

**开发完成时间**: 2025-01-21
**版本**: v1.0.0
**状态**: ✅ 生产就绪