#!/usr/bin/env python3
"""
验证下拉菜单修复是否成功
"""
import requests
import json
from datetime import datetime
import time

def verify_api_endpoints():
    """验证API端点"""
    print("🔍 验证API端点")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    endpoints = [
        ("/api/chatlog/contacts", "联系人API"),
        ("/api/chatlog/chatrooms", "群聊API"),
        ("/api/chatlog/status", "状态API")
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", proxies=proxies, timeout=10)
            data = response.json()
            
            if data.get('success'):
                if 'data' in data and isinstance(data['data'], list):
                    print(f"✅ {name}: 成功 ({len(data['data'])} 条记录)")
                else:
                    print(f"✅ {name}: 成功")
            else:
                print(f"❌ {name}: 失败 - {data.get('message')}")
        except Exception as e:
            print(f"❌ {name}: 异常 - {str(e)}")
    
    print()

def analyze_data_structure():
    """分析数据结构"""
    print("📊 分析数据结构")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 分析联系人数据
    try:
        response = requests.get(f"{base_url}/api/chatlog/contacts", proxies=proxies, timeout=10)
        contacts_data = response.json()
        
        if contacts_data.get('success') and contacts_data.get('data'):
            contacts = contacts_data['data']
            print(f"📋 联系人数据分析:")
            print(f"   总数量: {len(contacts)}")
            
            if len(contacts) > 0:
                sample = contacts[0]
                print(f"   字段: {list(sample.keys())}")
                print(f"   示例: userName={sample.get('userName')}, nickName={sample.get('nickName')}")
                
                # 统计有效联系人
                valid_contacts = [c for c in contacts if c.get('userName') and c.get('nickName')]
                print(f"   有效联系人: {len(valid_contacts)}")
                
                # 显示前5个联系人
                print(f"   前5个联系人:")
                for i, contact in enumerate(valid_contacts[:5]):
                    display_name = contact.get('remark') or contact.get('nickName', 'Unknown')
                    print(f"     {i+1}. {display_name} ({contact.get('userName', 'No ID')})")
    except Exception as e:
        print(f"❌ 联系人数据分析失败: {str(e)}")
    
    print()
    
    # 分析群聊数据
    try:
        response = requests.get(f"{base_url}/api/chatlog/chatrooms", proxies=proxies, timeout=10)
        chatrooms_data = response.json()
        
        if chatrooms_data.get('success') and chatrooms_data.get('data'):
            chatrooms = chatrooms_data['data']
            print(f"👥 群聊数据分析:")
            print(f"   总数量: {len(chatrooms)}")
            
            if len(chatrooms) > 0:
                sample = chatrooms[0]
                print(f"   字段: {list(sample.keys())}")
                print(f"   示例: name={sample.get('name')}, nickName={sample.get('nickName')}")
                
                # 统计有效群聊
                valid_chatrooms = [c for c in chatrooms if c.get('name') and c.get('nickName')]
                print(f"   有效群聊: {len(valid_chatrooms)}")
                
                # 按成员数量排序
                sorted_chatrooms = sorted(valid_chatrooms, 
                                        key=lambda x: len(x.get('users', [])), 
                                        reverse=True)
                
                print(f"   按成员数量排序的前5个群聊:")
                for i, chatroom in enumerate(sorted_chatrooms[:5]):
                    member_count = len(chatroom.get('users', []))
                    print(f"     {i+1}. {chatroom.get('nickName', 'Unknown')} ({member_count}人)")
    except Exception as e:
        print(f"❌ 群聊数据分析失败: {str(e)}")
    
    print()

def generate_javascript_test():
    """生成JavaScript测试代码"""
    print("🖥️  JavaScript测试代码")
    print("=" * 50)
    
    js_code = """
// 在浏览器控制台中运行以下代码来测试修复效果

// 1. 检查全局函数是否可用
console.log('ChatlogApp对象:', typeof ChatlogApp);
console.log('可用函数:', Object.keys(ChatlogApp || {}));

// 2. 手动触发数据加载
if (typeof ChatlogApp !== 'undefined' && ChatlogApp.loadContactsAndGroups) {
    console.log('🚀 手动触发数据加载...');
    ChatlogApp.loadContactsAndGroups();
} else {
    console.log('❌ loadContactsAndGroups函数不可用');
}

// 3. 检查下拉菜单元素
const talkerSelect = document.getElementById('talker-select');
const groupSelect = document.getElementById('analysis-group');

console.log('联系人下拉菜单:', talkerSelect);
console.log('群聊下拉菜单:', groupSelect);

if (talkerSelect) {
    console.log('联系人选项数量:', talkerSelect.options.length);
    if (talkerSelect.options.length > 1) {
        console.log('前3个联系人选项:');
        for (let i = 1; i < Math.min(4, talkerSelect.options.length); i++) {
            console.log(`  ${i}. ${talkerSelect.options[i].textContent}`);
        }
    }
}

if (groupSelect) {
    console.log('群聊选项数量:', groupSelect.options.length);
    if (groupSelect.options.length > 1) {
        console.log('前3个群聊选项:');
        for (let i = 1; i < Math.min(4, groupSelect.options.length); i++) {
            console.log(`  ${i}. ${groupSelect.options[i].textContent}`);
        }
    }
}

// 4. 测试API调用
if (typeof ChatlogApp !== 'undefined' && ChatlogApp.apiRequest) {
    ChatlogApp.apiRequest('/api/chatlog/contacts').then(data => {
        console.log('联系人API测试结果:', data.success ? '成功' : '失败');
        console.log('联系人数量:', data.data ? data.data.length : 0);
    });
    
    ChatlogApp.apiRequest('/api/chatlog/chatrooms').then(data => {
        console.log('群聊API测试结果:', data.success ? '成功' : '失败');
        console.log('群聊数量:', data.data ? data.data.length : 0);
    });
}
"""
    
    print(js_code)

def main():
    """主函数"""
    print("🔧 下拉菜单修复验证")
    print("=" * 60)
    print(f"🕒 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 验证API端点
    verify_api_endpoints()
    
    # 分析数据结构
    analyze_data_structure()
    
    # 生成JavaScript测试代码
    generate_javascript_test()
    
    print("📋 修复总结:")
    print("1. ✅ 修复了API数据字段映射 (contactsResponse.contacts -> contactsResponse.data)")
    print("2. ✅ 修复了联系人字段名称 (wxid -> userName, displayName -> nickName)")
    print("3. ✅ 修复了群聊字段名称 (wxid -> name, displayName -> nickName)")
    print("4. ✅ 添加了数据过滤和排序功能")
    print("5. ✅ 添加了群聊搜索功能")
    print("6. ✅ 添加了详细的调试日志")
    print("7. ✅ 导出了函数到全局作用域")
    print()
    print("🌐 请访问 http://127.0.0.1:3000 并:")
    print("   1. 切换到'聊天记录'标签页，检查'群聊/联系人'下拉菜单")
    print("   2. 切换到'AI分析'标签页，检查'群聊名称'下拉菜单")
    print("   3. 打开浏览器开发者工具，查看控制台日志")
    print("   4. 在控制台中运行上面的JavaScript测试代码")

if __name__ == "__main__":
    main()
