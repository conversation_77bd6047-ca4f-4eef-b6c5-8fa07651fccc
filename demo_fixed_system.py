#!/usr/bin/env python3
"""
演示修复后的系统功能
"""
import requests
import json
import time
from datetime import datetime, timedelta

class ChatlogSystemDemo:
    def __init__(self):
        self.base_url = "http://127.0.0.1:3000"
        self.proxies = {'http': None, 'https': None}
    
    def api_call(self, endpoint, description=""):
        """调用API并显示结果"""
        url = f"{self.base_url}{endpoint}"
        try:
            response = requests.get(url, proxies=self.proxies, timeout=10)
            data = response.json()
            
            print(f"🔗 {description or endpoint}")
            print(f"   状态: {'✅ 成功' if data.get('success') else '❌ 失败'}")
            
            if data.get('success'):
                if 'data' in data:
                    if isinstance(data['data'], list):
                        print(f"   数据: {len(data['data'])} 条记录")
                        if len(data['data']) > 0:
                            print(f"   示例: {str(data['data'][0])[:100]}...")
                    elif isinstance(data['data'], dict):
                        print(f"   数据: {data['data']}")
                if 'message' in data:
                    print(f"   消息: {data['message']}")
            else:
                print(f"   错误: {data.get('message', '未知错误')}")
            
            print()
            return data
        except Exception as e:
            print(f"❌ 请求失败: {str(e)}\n")
            return None
    
    def demo_system_status(self):
        """演示系统状态检查"""
        print("🏥 系统状态检查")
        print("=" * 50)
        
        # 检查Chatlog服务状态
        self.api_call("/api/chatlog/status", "Chatlog服务状态")
        
        # 检查数据统计
        contacts = self.api_call("/api/chatlog/contacts", "联系人数据")
        chatrooms = self.api_call("/api/chatlog/chatrooms", "群聊数据")
        sessions = self.api_call("/api/chatlog/sessions", "会话数据")
        
        if contacts and chatrooms and sessions:
            print("📊 数据统计汇总:")
            print(f"   联系人: {len(contacts.get('data', []))} 个")
            print(f"   群聊: {len(chatrooms.get('data', []))} 个")
            print(f"   会话: {len(sessions.get('data', []))} 个")
            print()
    
    def demo_chatlog_query(self):
        """演示聊天记录查询功能"""
        print("💬 聊天记录查询演示")
        print("=" * 50)
        
        # 查询今天的记录
        today = datetime.now().strftime('%Y-%m-%d')
        self.api_call(f"/api/chatlog/chatlog?time={today}~{today}&limit=10", 
                     f"查询今天({today})的聊天记录")
        
        # 查询昨天的记录
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        self.api_call(f"/api/chatlog/chatlog?time={yesterday}~{yesterday}&limit=10", 
                     f"查询昨天({yesterday})的聊天记录")
        
        # 查询最近一周的记录
        week_ago = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        self.api_call(f"/api/chatlog/chatlog?time={week_ago}~{today}&limit=5", 
                     f"查询最近一周({week_ago}~{today})的聊天记录")
    
    def demo_frontend_features(self):
        """演示前端功能"""
        print("🖥️  前端功能演示")
        print("=" * 50)
        
        print("✨ 前端功能特性:")
        print("   1. 🎨 现代化苹果风格界面设计")
        print("   2. 📱 响应式布局，支持多设备")
        print("   3. 🔄 实时状态监控和更新")
        print("   4. 📊 数据可视化展示")
        print("   5. 🔍 智能搜索和筛选")
        print("   6. ⚡ 快速响应的用户交互")
        print()
        
        print("🌐 访问地址: http://127.0.0.1:3000")
        print("📋 主要功能页面:")
        print("   • 概览 - 系统状态和数据统计")
        print("   • 聊天记录 - 查询和浏览聊天记录")
        print("   • AI分析 - 智能分析功能")
        print("   • 定时任务 - 自动化任务管理")
        print("   • 分析历史 - 历史记录查看")
        print()
    
    def demo_api_performance(self):
        """演示API性能"""
        print("⚡ API性能测试")
        print("=" * 50)
        
        endpoints = [
            ("/api/chatlog/status", "状态检查"),
            ("/api/chatlog/contacts", "联系人列表"),
            ("/api/chatlog/chatrooms", "群聊列表"),
            ("/api/chatlog/sessions", "会话列表"),
        ]
        
        for endpoint, name in endpoints:
            start_time = time.time()
            result = self.api_call(endpoint, f"{name}性能测试")
            end_time = time.time()
            
            if result:
                response_time = (end_time - start_time) * 1000
                print(f"   ⏱️  响应时间: {response_time:.2f}ms")
                print()
    
    def run_demo(self):
        """运行完整演示"""
        print("🎭 Flask聊天记录查询与AI分析系统")
        print("🔧 修复后功能演示")
        print("=" * 60)
        print(f"🕒 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # 系统状态演示
        self.demo_system_status()
        
        # 聊天记录查询演示
        self.demo_chatlog_query()
        
        # 前端功能演示
        self.demo_frontend_features()
        
        # API性能演示
        self.demo_api_performance()
        
        print("🎉 演示完成！")
        print("=" * 60)
        print("✅ 所有功能正常运行")
        print("🚀 系统已准备就绪")
        print("🌟 可以开始使用了！")

if __name__ == "__main__":
    demo = ChatlogSystemDemo()
    demo.run_demo()
