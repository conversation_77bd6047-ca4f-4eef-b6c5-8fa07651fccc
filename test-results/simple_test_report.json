{"timestamp": "2025-07-20T22:21:30.439757", "summary": {"total": 10, "passed": 0, "failed": 10, "success_rate": 0.0}, "test_results": [{"test_name": "主页加载", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:16.079491"}, {"test_name": "标签元素", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:17.899984"}, {"test_name": "API健康检查", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:19.718416"}, {"test_name": "Chatlog服务连接", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:21.537871"}, {"test_name": "联系人API测试", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:23.356529"}, {"test_name": "群聊API测试", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:24.164823"}, {"test_name": "会话API测试", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:24.985515"}, {"test_name": "静态资源/static/css/style.css", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:26.798331"}, {"test_name": "静态资源/static/js/main.js", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:27.627491"}, {"test_name": "表单元素检查", "success": false, "message": "HTTP状态码: 502", "details": null, "timestamp": "2025-07-20T22:21:29.437840"}]}