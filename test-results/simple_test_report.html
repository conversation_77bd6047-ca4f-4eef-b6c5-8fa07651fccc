
        <!DOCTYPE html>
        <html>
        <head>
            <title>Flask聊天记录查询与AI分析系统 - 功能测试报告</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
                .summary-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #007cba; }
                .test-item { margin: 10px 0; padding: 15px; border-radius: 8px; border-left: 4px solid #ddd; }
                .test-success { border-left-color: #28a745; background: #f8fff9; }
                .test-failure { border-left-color: #dc3545; background: #fff8f8; }
                .test-name { font-weight: bold; margin-bottom: 5px; }
                .test-message { color: #666; }
                .test-details { background: #f1f3f4; padding: 10px; border-radius: 4px; margin-top: 10px; font-family: monospace; font-size: 12px; }
                .timestamp { color: #888; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎭 Flask聊天记录查询与AI分析系统</h1>
                    <h2>功能测试报告</h2>
                    <p>测试时间: 2025-07-20T22:21:30.439757</p>
                    <p>测试目标: http://127.0.0.1:3000</p>
                </div>
                
                <div class="summary">
                    <div class="summary-card">
                        <h3>总测试数</h3>
                        <h2>10</h2>
                    </div>
                    <div class="summary-card">
                        <h3>通过测试</h3>
                        <h2 style="color: #28a745;">0</h2>
                    </div>
                    <div class="summary-card">
                        <h3>失败测试</h3>
                        <h2 style="color: #dc3545;">10</h2>
                    </div>
                    <div class="summary-card">
                        <h3>成功率</h3>
                        <h2 style="color: #007cba;">0.0%</h2>
                    </div>
                </div>
                
                <h2>📋 详细测试结果</h2>
        
                <div class="test-item test-failure">
                    <div class="test-name">❌ 主页加载</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:16.079491</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 标签元素</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:17.899984</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ API健康检查</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:19.718416</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ Chatlog服务连接</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:21.537871</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 联系人API测试</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:23.356529</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 群聊API测试</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:24.164823</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 会话API测试</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:24.985515</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 静态资源/static/css/style.css</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:26.798331</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 静态资源/static/js/main.js</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:27.627491</div>
            </div>
                <div class="test-item test-failure">
                    <div class="test-name">❌ 表单元素检查</div>
                    <div class="test-message">HTTP状态码: 502</div>
                    <div class="timestamp">2025-07-20T22:21:29.437840</div>
            </div>
            </div>
        </body>
        </html>
        