"""
Flask应用启动文件
"""
import os
from app import create_app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    # 从环境变量获取配置
    host = os.environ.get('APP_HOST', '0.0.0.0')
    port = int(os.environ.get('APP_PORT', 3000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'

    print(f"启动Flask应用...")
    print(f"访问地址: http://{host}:{port}")
    print(f"调试模式: {debug}")

    app.run(host=host, port=port, debug=debug)