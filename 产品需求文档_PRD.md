# 聊天记录查询与AI分析系统 - 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 产品定义
聊天记录查询与AI分析系统是一个基于Node.js和Express框架的中文聊天记录查询与AI智能分析系统，提供便捷的聊天数据检索、可视化分析和AI驱动的深度洞察功能。

### 1.2 产品目标
- 为用户提供高效的微信聊天记录查询和管理功能
- 通过AI技术实现聊天内容的智能分析和洞察
- 提供现代化、响应式的Web界面体验
- 支持定时任务和批量分析功能

### 1.3 目标用户
- 需要分析微信群聊内容的个人用户
- 希望从聊天记录中提取有价值信息的研究人员
- 需要定期分析社群活跃度的社群管理员
- 对聊天数据进行深度挖掘的数据分析师

### 1.4 核心价值
- **数据洞察**: 通过AI分析提取聊天记录中的关键信息和趋势
- **效率提升**: 自动化分析替代人工阅读大量聊天记录
- **可视化展示**: 生成美观的HTML报告和图表
- **定时自动化**: 支持定时任务，实现无人值守的定期分析

## 2. 当前架构分析

### 2.1 技术栈
**后端技术栈:**
- 运行环境: Node.js v18+
- Web框架: Express.js
- HTTP客户端: Axios
- 任务调度: node-cron
- 模板引擎: EJS
- 环境管理: dotenv

**前端技术栈:**
- 界面框架: 原生JavaScript + HTML5
- 样式设计: CSS3 + Flexbox Grid (苹果风格设计)
- 图标库: Font Awesome 6.0
- 交互体验: 现代化响应式设计

**AI集成:**
- DeepSeek (deepseek-chat, deepseek-reasoner)
- Google Gemini (gemini-pro, gemini-pro-vision)
- 统一AI调用接口，支持模型切换

### 2.2 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端Web界面    │────│  Express服务器   │────│   Chatlog API   │
│  (HTML/CSS/JS)  │    │   (Node.js)     │    │  (端口:5030)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   AI服务集成     │
                       │ (DeepSeek/Gemini)│
                       └─────────────────┘
```

### 2.3 数据流
1. 用户通过Web界面发起查询请求
2. Express服务器接收请求并转发给Chatlog API
3. Chatlog API从微信数据库中提取聊天记录
4. 服务器处理数据并返回给前端展示
5. AI分析请求通过统一接口调用外部AI服务
6. 分析结果保存为HTML文件并展示给用户

## 3. 功能需求详述

### 3.1 聊天记录查询功能

#### 3.1.1 基础查询
**功能描述**: 支持多维度的聊天记录搜索和筛选

**具体需求**:
- 时间范围筛选: 支持预设时间段(今天、昨天、最近一周等)和自定义时间范围
- 联系人筛选: 支持选择特定联系人或群聊进行查询
- 关键词搜索: 在聊天内容中进行关键词匹配搜索
- 数据量控制: 支持限制返回记录数量(100-10000条)或无限制查询
- 分页浏览: 支持大数据量的分页显示

**输入参数**:
- time: 时间范围 (格式: YYYY-MM-DD~YYYY-MM-DD)
- talker: 联系人/群聊标识符
- limit: 返回记录数量限制
- offset: 分页偏移量
- format: 返回数据格式 (json/csv)

**输出格式**:
```json
{
  "success": true,
  "data": [
    {
      "msgid": "消息ID",
      "timestamp": "时间戳",
      "talker": "发送者",
      "content": "消息内容",
      "type": "消息类型",
      "media": "媒体文件信息"
    }
  ],
  "total": "总记录数",
  "page": "当前页码"
}
```

#### 3.1.2 联系人和群聊管理
**功能描述**: 获取和管理联系人及群聊列表

**具体需求**:
- 获取所有联系人列表，包含昵称、备注、微信号等信息
- 获取所有群聊列表，包含群名、成员数量、群主等信息
- 支持联系人和群聊的搜索和筛选
- 显示最近会话列表

**数据格式**:
```json
{
  "contacts": [
    {
      "wxid": "微信ID",
      "displayName": "显示名称", 
      "nickname": "昵称",
      "remark": "备注",
      "alias": "微信号"
    }
  ],
  "chatrooms": [
    {
      "wxid": "群聊ID", 
      "displayName": "群聊名称",
      "userCount": "成员数量",
      "owner": "群主"
    }
  ]
}
```

### 3.2 AI智能分析功能

#### 3.2.1 预设分析模板
**功能描述**: 提供多种预设的分析模板，满足不同场景需求

**预设模板类型**:
1. **编程技术讨论分析**: 分析技术交流内容、代码分享、问题解决等
2. **科学学习内容分析**: 总结学习讨论要点、知识分享等
3. **阅读讨论分析**: 分析读书分享内容、观点交流等
4. **通用聊天分析**: 分析聊天话题、活跃度、情感倾向等

**模板配置**:
```json
{
  "templateId": "模板ID",
  "name": "模板名称", 
  "description": "模板描述",
  "prompt": "AI提示词模板",
  "systemPrompt": "系统提示词",
  "category": "模板分类"
}
```

#### 3.2.2 自定义分析
**功能描述**: 支持用户自定义分析提示词，实现个性化分析需求

**具体需求**:
- 自定义AI提示词输入
- 支持多种分析维度配置
- 时间范围和群聊选择
- 分析结果的HTML格式输出

#### 3.2.3 批量分析
**功能描述**: 支持一键分析多个群聊或分析项

**具体需求**:
- 批量选择多个群聊进行分析
- 支持多个分析模板同时执行
- 实时显示批量分析进度
- 支持取消正在进行的批量分析
- 批量分析结果统一管理

### 3.3 定时任务功能

#### 3.3.1 定时分析配置
**功能描述**: 支持配置定时任务，实现自动化分析

**具体需求**:
- Cron表达式配置执行时间
- 支持启用/禁用定时任务
- 配置定时分析的群聊和分析项
- 定时任务执行状态监控

**配置格式**:
```json
{
  "enabled": true,
  "cronTime": "0 0 8 * * *",
  "analysisItems": [
    {
      "id": "分析项ID",
      "name": "分析项名称",
      "groupName": "群聊名称",
      "analysisType": "分析类型",
      "customPrompt": "自定义提示词"
    }
  ]
}
```

#### 3.3.2 任务执行和监控
**功能描述**: 定时任务的执行和状态监控

**具体需求**:
- 自动执行定时分析任务
- 任务执行日志记录
- 手动触发定时任务
- 任务执行结果通知

### 3.4 分析历史管理

#### 3.4.1 历史记录存储
**功能描述**: 保存和管理AI分析的历史记录

**具体需求**:
- 分析结果自动保存为HTML文件
- 分析元数据记录(时间、群聊、分析类型等)
- 历史记录列表展示
- 支持历史记录的查看和删除

**元数据格式**:
```json
{
  "historyId": "历史记录ID",
  "groupName": "群聊名称",
  "analysisType": "分析类型", 
  "timeRange": "时间范围",
  "messageCount": "消息数量",
  "timestamp": "分析时间",
  "title": "分析标题"
}
```

## 4. 用户界面需求

### 4.1 整体设计风格
- **设计语言**: 苹果风格设计，简洁现代
- **色彩方案**: 以白色和浅灰为主，蓝色作为主色调
- **字体**: -apple-system, BlinkMacSystemFont, 'SF Pro Display'
- **响应式**: 支持桌面端和移动端适配

### 4.2 页面布局结构

#### 4.2.1 顶部工具栏
**组件**: 
- 系统标题和版本号
- 模型设置按钮
- 刷新连接按钮  
- 连接状态指示器

#### 4.2.2 AI智能分析中心
**组件**:
- 一键全分析按钮
- 批量分析进度显示
- 动态分析项网格
- 新增分析项按钮
- 自定义分析表单

#### 4.2.3 定时任务管理区域
**组件**:
- 定时任务状态显示
- 手动触发按钮
- 配置定时任务按钮
- 任务详情浮窗

#### 4.2.4 聊天记录查询区域
**组件**:
- 查询表单(时间、联系人、关键词)
- 快捷操作按钮
- 聊天记录展示区域
- 分页控件

#### 4.2.5 分析历史记录区域
**组件**:
- 历史记录列表
- 记录详情查看
- 删除操作按钮

### 4.3 交互设计

#### 4.3.1 状态反馈
- 加载状态: 显示加载动画和进度
- 成功状态: 绿色提示和成功图标
- 错误状态: 红色提示和错误信息
- 连接状态: 实时显示连接状态指示器

#### 4.3.2 用户操作流程
1. **查询聊天记录**: 选择时间范围 → 选择联系人 → 输入关键词 → 点击查询 → 查看结果
2. **AI分析**: 选择分析类型 → 配置参数 → 执行分析 → 查看报告
3. **批量分析**: 点击一键全分析 → 监控进度 → 查看结果
4. **定时任务**: 配置任务 → 启用定时 → 监控执行状态

## 5. 技术需求

### 5.1 性能要求
- 页面加载时间: < 3秒
- API响应时间: < 5秒
- 大数据量查询: 支持10万+条记录
- 并发用户: 支持10个并发用户

### 5.2 兼容性要求
- 浏览器: Chrome 80+, Firefox 75+, Safari 13+, Edge 80+
- 操作系统: Windows 10+, macOS 10.15+, Linux
- 移动端: iOS 13+, Android 8+

### 5.3 安全要求
- API Key安全存储
- 数据传输加密
- 用户输入验证和过滤
- 防止SQL注入和XSS攻击

## 6. 非功能性需求

### 6.1 可用性
- 系统可用性: 99.5%
- 故障恢复时间: < 5分钟
- 数据备份: 每日自动备份

### 6.2 可扩展性
- 支持新增AI模型
- 支持新增分析模板
- 支持多语言扩展
- 支持插件化架构

### 6.3 可维护性
- 代码注释覆盖率: > 80%
- 模块化设计
- 统一的错误处理机制
- 详细的日志记录

## 7. 依赖关系

### 7.1 外部依赖
- **Chatlog服务**: 微信数据解密和HTTP API服务 (端口5030)
- **DeepSeek API**: AI分析服务
- **Google Gemini API**: 备用AI分析服务
- **Font Awesome**: 图标库CDN

### 7.2 系统依赖
- Node.js 18+
- npm 8+
- 微信数据库文件访问权限

### 7.3 第三方库
```json
{
  "express": "^4.18.2",
  "axios": "^1.4.0", 
  "node-cron": "^3.0.2",
  "ejs": "^3.1.9",
  "dotenv": "^16.3.1",
  "cors": "^2.8.5"
}
```

## 8. 数据模型

### 8.1 聊天消息数据模型
```javascript
{
  msgid: String,           // 消息ID
  timestamp: Date,         // 时间戳
  talker: String,          // 发送者ID
  content: String,         // 消息内容
  type: Number,            // 消息类型
  media: Object,           // 媒体文件信息
  roomid: String,          // 群聊ID (如果是群聊)
  sender: String           // 实际发送者 (群聊中)
}
```

### 8.2 分析历史数据模型
```javascript
{
  historyId: String,       // 历史记录ID
  groupName: String,       // 群聊名称
  analysisType: String,    // 分析类型
  customPrompt: String,    // 自定义提示词
  timeRange: String,       // 时间范围
  messageCount: Number,    // 消息数量
  timestamp: Date,         // 分析时间
  title: String,           // 分析标题
  filePath: String         // HTML文件路径
}
```

### 8.3 定时任务配置模型
```javascript
{
  enabled: Boolean,        // 是否启用
  cronTime: String,        // Cron表达式
  analysisItems: [{        // 分析项列表
    id: String,
    name: String,
    groupName: String,
    analysisType: String,
    customPrompt: String
  }]
}
```

## 9. API接口规范

### 9.1 聊天记录相关接口
- `GET /api/chatlog` - 获取聊天记录
- `GET /api/contacts` - 获取联系人列表  
- `GET /api/chatrooms` - 获取群聊列表
- `GET /api/sessions` - 获取会话列表
- `GET /api/media` - 获取多媒体内容

### 9.2 AI分析相关接口
- `POST /api/ai-analysis` - 执行AI分析
- `GET /api/analysis-history` - 获取分析历史
- `GET /api/analysis-history/:id` - 获取特定分析记录
- `DELETE /api/analysis-history/:id` - 删除分析记录

### 9.3 模型设置相关接口
- `GET /api/model-settings` - 获取模型设置
- `POST /api/model-settings` - 保存模型设置
- `POST /api/model-settings/test` - 测试模型连接

### 9.4 定时任务相关接口
- `GET /api/scheduled-analysis-status` - 获取定时任务状态
- `POST /api/scheduled-analysis-config` - 配置定时任务
- `POST /api/trigger-scheduled-analysis` - 手动触发定时任务

## 10. 错误处理需求

### 10.1 错误分类
- **连接错误**: Chatlog服务不可用
- **认证错误**: AI API Key无效
- **数据错误**: 查询参数无效或数据格式错误
- **系统错误**: 服务器内部错误

### 10.2 错误处理策略
- 统一的错误响应格式
- 用户友好的错误提示
- 详细的错误日志记录
- 自动重试机制

### 10.3 错误响应格式
```json
{
  "success": false,
  "error": "错误类型",
  "message": "用户友好的错误信息",
  "details": "详细错误信息",
  "code": "错误代码"
}
```

## 11. 用户故事和用例

### 11.1 核心用户故事

#### 故事1: 查询历史聊天记录
**作为** 一个用户
**我希望** 能够快速查询特定时间段的聊天记录
**以便于** 找到之前讨论的重要信息

**验收标准**:
- 可以选择时间范围(今天、昨天、最近一周、自定义)
- 可以选择特定的联系人或群聊
- 可以输入关键词进行内容搜索
- 查询结果以类似微信的聊天界面展示
- 支持分页浏览大量数据

#### 故事2: AI智能分析群聊内容
**作为** 一个群聊管理员
**我希望** 能够使用AI分析群聊的讨论内容
**以便于** 了解群内的主要话题和活跃情况

**验收标准**:
- 可以选择预设的分析模板(编程、科学、阅读等)
- 可以自定义分析提示词
- 分析结果以HTML报告形式展示
- 包含图表、统计数据和文字总结
- 分析历史可以保存和回顾

#### 故事3: 批量分析多个群聊
**作为** 一个数据分析师
**我希望** 能够一键分析多个群聊
**以便于** 提高工作效率，获得综合性的分析报告

**验收标准**:
- 一键启动所有配置的分析项
- 实时显示批量分析进度
- 可以随时取消正在进行的批量分析
- 批量分析完成后统一展示结果
- 支持导出批量分析报告

#### 故事4: 配置定时自动分析
**作为** 一个社群运营者
**我希望** 能够设置定时任务自动分析群聊
**以便于** 定期了解社群动态，无需手动操作

**验收标准**:
- 可以使用Cron表达式配置执行时间
- 可以配置多个分析项和目标群聊
- 可以启用/禁用定时任务
- 可以手动触发定时任务进行测试
- 定时任务执行状态可以实时查看

### 11.2 详细用例描述

#### 用例1: 查询群聊聊天记录
**前置条件**: Chatlog服务正常运行，微信数据已解密
**主要流程**:
1. 用户打开系统首页
2. 在查询表单中选择时间范围
3. 从下拉列表中选择目标群聊
4. (可选)输入关键词进行内容筛选
5. 点击"查询"按钮
6. 系统显示查询结果，以聊天气泡形式展示
7. 用户可以点击分页按钮浏览更多记录

**异常流程**:
- 如果Chatlog服务不可用，显示连接错误提示
- 如果查询无结果，显示"未找到匹配记录"
- 如果查询参数无效，显示参数错误提示

#### 用例2: 执行AI分析
**前置条件**: AI模型已配置，API Key有效
**主要流程**:
1. 用户在AI分析区域选择分析类型
2. 配置分析参数(时间范围、目标群聊)
3. (可选)输入自定义提示词
4. 点击"执行分析"按钮
5. 系统显示分析进度和状态
6. 分析完成后，系统生成HTML报告
7. 用户可以查看分析结果并保存到历史记录

**异常流程**:
- 如果AI服务不可用，显示服务错误提示
- 如果API Key无效，提示重新配置
- 如果分析超时，提供重试选项

## 12. 数据流和业务逻辑

### 12.1 聊天记录查询流程
```
用户输入查询条件 → 参数验证 → 构建API请求 → 调用Chatlog API →
数据格式化 → 分页处理 → 返回前端展示 → 用户交互(分页/详情查看)
```

### 12.2 AI分析处理流程
```
选择分析类型 → 获取聊天数据 → 构建AI提示词 → 调用AI API →
处理AI响应 → 生成HTML报告 → 保存历史记录 → 展示分析结果
```

### 12.3 批量分析处理流程
```
获取分析项配置 → 创建分析队列 → 逐个执行分析 → 更新进度状态 →
处理分析结果 → 生成综合报告 → 通知用户完成
```

### 12.4 定时任务执行流程
```
Cron触发 → 检查任务配置 → 获取昨日聊天数据 → 执行配置的分析项 →
生成分析报告 → 保存结果 → 记录执行日志
```

## 13. 输入输出规范

### 13.1 查询接口输入输出

#### 输入参数规范
```javascript
// GET /api/chatlog
{
  time: "2024-01-01~2024-12-31",  // 时间范围，必填
  talker: "群聊ID或联系人ID",        // 可选
  limit: 100,                     // 返回数量限制，可选
  offset: 0,                      // 分页偏移，可选
  format: "json"                  // 返回格式，默认json
}
```

#### 输出格式规范
```javascript
// 成功响应
{
  "success": true,
  "data": [
    {
      "msgid": "123456789",
      "timestamp": "2024-01-01T10:30:00Z",
      "talker": "群聊ID",
      "sender": "发送者ID",
      "content": "消息内容",
      "type": 1,  // 1:文本 3:图片 34:语音 43:视频 47:表情 49:文件
      "media": {
        "filename": "文件名",
        "size": 1024,
        "url": "/api/media?msgid=123456789"
      }
    }
  ],
  "pagination": {
    "total": 1500,
    "page": 1,
    "pageSize": 100,
    "hasNext": true
  }
}
```

### 13.2 AI分析接口输入输出

#### 输入参数规范
```javascript
// POST /api/ai-analysis
{
  "groupName": "群聊名称",           // 必填
  "analysisType": "programming",    // 分析类型，必填
  "customPrompt": "自定义提示词",    // 可选
  "timeRange": "2024-01-01~2024-01-31"  // 时间范围，可选
}
```

#### 输出格式规范
```javascript
// 成功响应
{
  "success": true,
  "historyId": "analysis_20240101_123456",
  "title": "群聊名称 - 编程技术讨论分析",
  "metadata": {
    "groupName": "群聊名称",
    "analysisType": "programming",
    "timeRange": "2024-01-01~2024-01-31",
    "messageCount": 150,
    "timestamp": "2024-01-01T15:30:00Z"
  }
}
```

## 14. 认证和授权需求

### 14.1 当前认证机制
- 系统当前为单用户本地部署，无需用户认证
- API Key通过环境变量或配置文件管理
- 所有功能对本地用户开放

### 14.2 未来扩展考虑
如需支持多用户，建议实现以下认证机制：
- JWT Token认证
- 用户角色权限管理
- API访问频率限制
- 数据隔离和权限控制

## 15. 部署和运维需求

### 15.1 部署环境要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 18.0.0或更高版本
- **内存**: 最低2GB，推荐4GB
- **存储**: 最低10GB可用空间
- **网络**: 需要访问外部AI API服务

### 15.2 部署步骤
1. 安装Node.js和npm
2. 克隆项目代码
3. 安装依赖: `npm install`
4. 配置环境变量(.env文件)
5. 启动Chatlog服务(端口5030)
6. 启动Web服务: `npm start`
7. 访问http://localhost:3000

### 15.3 监控和日志
- **应用日志**: 记录API调用、错误信息、用户操作
- **性能监控**: 响应时间、内存使用、CPU占用
- **错误追踪**: 异常堆栈、错误频率统计
- **业务监控**: AI分析成功率、定时任务执行状态

## 16. 测试需求

### 16.1 功能测试
- **查询功能**: 各种查询条件组合测试
- **AI分析**: 不同分析类型和参数测试
- **批量操作**: 批量分析的并发和异常处理
- **定时任务**: Cron表达式解析和任务执行
- **历史管理**: 记录保存、查看、删除功能

### 16.2 性能测试
- **并发测试**: 多用户同时访问
- **大数据量测试**: 10万+条聊天记录查询
- **长时间运行**: 24小时稳定性测试
- **内存泄漏**: 长期运行内存使用监控

### 16.3 兼容性测试
- **浏览器兼容**: Chrome、Firefox、Safari、Edge
- **操作系统兼容**: Windows、macOS、Linux
- **移动端适配**: iOS Safari、Android Chrome
- **屏幕分辨率**: 1920x1080、1366x768、移动端各尺寸

### 16.4 安全测试
- **输入验证**: SQL注入、XSS攻击防护
- **API安全**: 参数验证、频率限制
- **数据安全**: 敏感信息加密存储
- **网络安全**: HTTPS传输、API Key保护

## 17. 风险评估和缓解策略

### 17.1 技术风险
**风险**: Chatlog服务不稳定或不可用
**影响**: 无法查询聊天记录，系统核心功能失效
**缓解策略**:
- 实现连接状态监控和自动重试
- 提供离线模式或缓存机制
- 增加服务健康检查和告警

**风险**: AI API服务限制或费用超支
**影响**: AI分析功能不可用，影响用户体验
**缓解策略**:
- 支持多个AI服务提供商切换
- 实现API调用频率控制
- 增加费用监控和预警机制

### 17.2 业务风险
**风险**: 微信数据格式变更
**影响**: 数据解析失败，查询结果异常
**缓解策略**:
- 定期更新Chatlog工具版本
- 增加数据格式兼容性检查
- 提供数据格式转换工具

**风险**: 用户隐私和数据安全
**影响**: 敏感聊天数据泄露风险
**缓解策略**:
- 本地部署，数据不上传云端
- 加密存储敏感配置信息
- 提供数据清理和删除功能

### 17.3 运维风险
**风险**: 系统资源耗尽
**影响**: 服务响应缓慢或崩溃
**缓解策略**:
- 实现资源使用监控
- 增加内存和CPU使用限制
- 提供系统资源清理功能

## 18. 未来扩展规划

### 18.1 功能扩展
- **多平台支持**: 支持QQ、钉钉等其他聊天平台
- **高级分析**: 情感分析、关系网络分析、趋势预测
- **数据可视化**: 更丰富的图表和统计展示
- **导出功能**: 支持PDF、Excel等格式导出
- **搜索增强**: 全文搜索、语义搜索、图片OCR搜索

### 18.2 技术升级
- **前端框架**: 考虑升级到React或Vue.js
- **数据库**: 引入专业数据库存储分析结果
- **缓存机制**: Redis缓存提升查询性能
- **微服务架构**: 拆分为多个独立服务
- **容器化部署**: Docker容器化部署

### 18.3 用户体验优化
- **个性化配置**: 用户自定义界面和功能
- **智能推荐**: 基于使用习惯的功能推荐
- **快捷操作**: 键盘快捷键、批量操作优化
- **多语言支持**: 国际化和本地化
- **无障碍访问**: 支持屏幕阅读器等辅助功能

---

**文档版本**: v1.0
**创建日期**: 2025-01-21
**最后更新**: 2025-01-21
**文档状态**: 待审核

**Flask重构参考说明**:
本PRD文档详细描述了当前Node.js/Express系统的完整功能和技术实现。在使用Flask重构时，可以参考以下对应关系：
- Express路由 → Flask Blueprint
- EJS模板 → Jinja2模板
- Node.js异步处理 → Python asyncio或多线程
- npm包管理 → pip/poetry包管理
- node-cron → APScheduler定时任务
- 前端JavaScript保持不变，仅需调整API调用地址
