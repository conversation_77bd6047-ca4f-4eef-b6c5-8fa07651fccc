#!/usr/bin/env python3
"""
测试搜索功能和时间修复
"""
import requests
import json
from datetime import datetime

def test_time_formatting():
    """测试时间格式化功能"""
    print("🕒 测试时间格式化功能")
    print("=" * 50)
    
    # 生成JavaScript测试代码
    js_test_code = """
// 在浏览器控制台中运行以下代码测试时间格式化

// 测试不同的时间格式
console.log('测试时间格式化功能:');

// 测试毫秒级时间戳
const timestamp1 = Date.now();
console.log('毫秒级时间戳:', timestamp1, '->', ChatlogApp.formatTime(timestamp1));

// 测试秒级时间戳
const timestamp2 = Math.floor(Date.now() / 1000);
console.log('秒级时间戳:', timestamp2, '->', ChatlogApp.formatTime(timestamp2));

// 测试字符串时间
const timestamp3 = new Date().toISOString();
console.log('ISO字符串:', timestamp3, '->', ChatlogApp.formatTime(timestamp3));

// 测试无效时间
console.log('无效时间 null:', ChatlogApp.formatTime(null));
console.log('无效时间 undefined:', ChatlogApp.formatTime(undefined));
console.log('无效时间 "invalid":', ChatlogApp.formatTime("invalid"));

// 测试边界值
console.log('小数值 (秒级):', ChatlogApp.formatTime(1640995200)); // 2022-01-01
console.log('大数值 (毫秒级):', ChatlogApp.formatTime(1640995200000)); // 2022-01-01
"""
    
    print("请在浏览器控制台中运行以下代码:")
    print(js_test_code)

def test_search_functionality():
    """测试搜索功能"""
    print("\n🔍 测试搜索功能")
    print("=" * 50)
    
    js_search_test = """
// 在浏览器控制台中运行以下代码测试搜索功能

// 1. 切换到群聊模式并测试搜索
console.log('=== 测试群聊搜索功能 ===');
const typeSelect = document.getElementById('talker-type-select');
typeSelect.value = 'chatroom';
typeSelect.dispatchEvent(new Event('change'));

// 等待数据加载完成后，测试搜索
setTimeout(() => {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        console.log('找到搜索输入框');
        
        // 测试搜索
        searchInput.value = 'AI';
        searchInput.dispatchEvent(new Event('input'));
        
        setTimeout(() => {
            const talkerSelect = document.getElementById('talker-select');
            console.log('搜索"AI"后的选项数量:', talkerSelect.options.length);
            
            // 清空搜索
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                console.log('清空搜索后的选项数量:', talkerSelect.options.length);
            }, 100);
        }, 100);
    } else {
        console.log('未找到搜索输入框');
    }
}, 2000);

// 2. 切换到联系人模式并测试搜索
setTimeout(() => {
    console.log('=== 测试联系人搜索功能 ===');
    typeSelect.value = 'contact';
    typeSelect.dispatchEvent(new Event('change'));
    
    setTimeout(() => {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            console.log('找到联系人搜索输入框');
            
            // 测试搜索
            searchInput.value = '家';
            searchInput.dispatchEvent(new Event('input'));
            
            setTimeout(() => {
                const talkerSelect = document.getElementById('talker-select');
                console.log('搜索"家"后的选项数量:', talkerSelect.options.length);
            }, 100);
        }
    }, 2000);
}, 5000);
"""
    
    print("请在浏览器控制台中运行以下代码:")
    print(js_search_test)

def test_chatlog_query_with_search():
    """测试带搜索的聊天记录查询"""
    print("\n💬 测试聊天记录查询")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:3000"
    proxies = {'http': None, 'https': None}
    
    # 创建一些模拟数据来测试时间格式化
    mock_data = [
        {
            "timestamp": 1640995200,  # 秒级时间戳
            "talker": "测试用户1",
            "content": "这是一条测试消息"
        },
        {
            "nTime": "2025-07-20T10:30:00Z",  # ISO字符串
            "sender": "测试用户2", 
            "message": "这是另一条测试消息"
        },
        {
            "time": 1640995200000,  # 毫秒级时间戳
            "from": "测试用户3",
            "text": "第三条测试消息"
        }
    ]
    
    print("模拟数据测试:")
    for i, data in enumerate(mock_data):
        print(f"  数据 {i+1}: {data}")
    
    print("\n在浏览器中测试:")
    print("1. 访问 http://127.0.0.1:3000")
    print("2. 切换到'聊天记录'标签页")
    print("3. 选择查询类型（群聊或联系人）")
    print("4. 使用搜索功能找到特定的群聊或联系人")
    print("5. 设置时间范围并查询")
    print("6. 检查查询结果中的时间显示是否正确")

def generate_comprehensive_test():
    """生成综合测试指南"""
    print("\n🧪 综合测试指南")
    print("=" * 50)
    
    print("请按以下步骤进行完整测试:")
    print()
    print("1. 📋 基础功能测试:")
    print("   - 访问 http://127.0.0.1:3000")
    print("   - 切换到'聊天记录'标签页")
    print("   - 验证双下拉菜单显示正确")
    print()
    print("2. 🔄 查询类型切换测试:")
    print("   - 选择'群聊' - 验证第二个下拉菜单加载群聊列表")
    print("   - 选择'联系人' - 验证第二个下拉菜单加载联系人列表")
    print("   - 选择'全部' - 验证第二个下拉菜单显示'全部'")
    print()
    print("3. 🔍 搜索功能测试:")
    print("   - 在群聊模式下，使用搜索框搜索群聊名称")
    print("   - 在联系人模式下，使用搜索框搜索联系人")
    print("   - 验证搜索结果实时更新")
    print("   - 验证清空搜索后恢复完整列表")
    print()
    print("4. 📊 数据排序验证:")
    print("   - 群聊按成员数量从高到低排序")
    print("   - 联系人按昵称字母顺序排序")
    print("   - 搜索结果保持排序规则")
    print()
    print("5. 🕒 时间显示测试:")
    print("   - 进行聊天记录查询")
    print("   - 验证时间显示不再是'Invalid Date'")
    print("   - 验证时间格式为中文本地化格式")
    print()
    print("6. 🎯 完整查询流程测试:")
    print("   - 选择查询类型")
    print("   - 使用搜索找到目标群聊/联系人")
    print("   - 设置时间范围")
    print("   - 执行查询")
    print("   - 验证结果显示正确")

def main():
    """主函数"""
    print("🔧 搜索功能和时间修复测试")
    print("=" * 60)
    print(f"🕒 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 测试时间格式化
    test_time_formatting()
    
    # 测试搜索功能
    test_search_functionality()
    
    # 测试聊天记录查询
    test_chatlog_query_with_search()
    
    # 生成综合测试指南
    generate_comprehensive_test()
    
    print("\n📋 修复总结:")
    print("1. ✅ 为群聊选择添加了搜索功能")
    print("2. ✅ 为联系人选择添加了搜索功能")
    print("3. ✅ 修复了时间格式化函数，支持多种时间格式")
    print("4. ✅ 改进了聊天记录显示，支持不同的字段名称")
    print("5. ✅ 添加了搜索输入框的样式和交互")
    print("6. ✅ 实现了实时搜索过滤功能")
    print()
    print("🌐 请访问 http://127.0.0.1:3000 测试新功能")

if __name__ == "__main__":
    main()
