#!/usr/bin/env python3
"""
调试Chatlog连接问题
"""
import requests
import json

def test_chatlog_direct():
    """直接测试Chatlog API"""
    base_url = "http://127.0.0.1:5030"

    endpoints = [
        "/api/v1/contact?format=json&limit=1",
        "/api/v1/chatroom?format=json&limit=1",
        "/api/v1/session?format=json&limit=1"
    ]

    for endpoint in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            print(f"Testing: {url}")

            headers = {
                'User-Agent': 'curl/8.9.1',
                'Accept': '*/*'
            }
            response = requests.get(url, headers=headers, timeout=10)
            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")

            if response.status_code == 200:
                data = response.json()
                print(f"Data keys: {list(data.keys())}")
                if 'items' in data:
                    print(f"Items count: {len(data['items'])}")
            else:
                print(f"Error response: {response.text}")

            print("-" * 50)

        except Exception as e:
            print(f"Exception: {e}")
            print("-" * 50)

def test_flask_service():
    """测试Flask服务中的ChatlogService"""
    from app import create_app
    from app.services.chatlog_service import ChatlogService

    app = create_app()

    with app.app_context():
        service = ChatlogService()

        try:
            print("Testing ChatlogService.test_connection()...")
            result = service.test_connection()
            print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        except Exception as e:
            print(f"Exception in test_connection: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    print("=== 直接测试Chatlog API ===")
    test_chatlog_direct()

    print("\n=== 测试Flask服务 ===")
    test_flask_service()