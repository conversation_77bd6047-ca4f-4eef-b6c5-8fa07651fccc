"""
Flask应用配置文件
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置类"""

    # Flask基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///chatlog.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True

    # Chatlog API配置
    CHATLOG_API_URL = os.environ.get('CHATLOG_API_URL') or 'http://localhost:5030'
    CHATLOG_API_TIMEOUT = int(os.environ.get('CHATLOG_API_TIMEOUT', 30))

    # AI服务配置
    DEEPSEEK_API_KEY = os.environ.get('DEEPSEEK_API_KEY')
    DEEPSEEK_API_URL = os.environ.get('DEEPSEEK_API_URL') or 'https://api.deepseek.com/v1/chat/completions'

    GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')
    GEMINI_API_URL = os.environ.get('GEMINI_API_URL') or 'https://generativelanguage.googleapis.com/v1beta/models'

    DEFAULT_AI_MODEL = os.environ.get('DEFAULT_AI_MODEL') or 'deepseek-chat'

    # 应用设置
    APP_TITLE = os.environ.get('APP_TITLE') or '聊天记录查询与AI分析系统'
    APP_VERSION = os.environ.get('APP_VERSION') or '1.0.0'

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = os.environ.get('LOG_FILE') or 'logs/app.log'

    # 分析设置
    MAX_MESSAGE_COUNT = int(os.environ.get('MAX_MESSAGE_COUNT', 10000))
    ANALYSIS_TIMEOUT = int(os.environ.get('ANALYSIS_TIMEOUT', 300))
    BATCH_ANALYSIS_CONCURRENCY = int(os.environ.get('BATCH_ANALYSIS_CONCURRENCY', 3))

    # 文件存储
    ANALYSIS_REPORTS_DIR = os.environ.get('ANALYSIS_REPORTS_DIR') or 'reports'
    STATIC_FILES_DIR = os.environ.get('STATIC_FILES_DIR') or 'static'
    UPLOAD_FOLDER = os.environ.get('UPLOAD_FOLDER') or 'uploads'

    # 确保目录存在
    @staticmethod
    def init_app(app):
        """初始化应用配置"""
        # 创建必要的目录
        dirs_to_create = [
            Config.ANALYSIS_REPORTS_DIR,
            Config.UPLOAD_FOLDER,
            'logs'
        ]

        for directory in dirs_to_create:
            if not os.path.exists(directory):
                os.makedirs(directory)

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

    # 生产环境的额外安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}