"""
API测试
"""
import pytest
import json
from app import create_app, db

@pytest.fixture
def app():
    """创建测试应用"""
    app = create_app('testing')

    with app.app_context():
        db.create_all()
        yield app
        db.drop_all()

@pytest.fixture
def client(app):
    """创建测试客户端"""
    return app.test_client()

def test_health_check(client):
    """测试健康检查接口"""
    response = client.get('/health')
    assert response.status_code == 200

    data = json.loads(response.data)
    assert data['status'] == 'ok'

def test_chatlog_status(client):
    """测试Chatlog状态检查"""
    response = client.get('/api/chatlog/status')
    assert response.status_code in [200, 503]  # 可能连接失败

    data = json.loads(response.data)
    assert 'success' in data

def test_analysis_history_empty(client):
    """测试空的分析历史"""
    response = client.get('/api/analysis-history')
    assert response.status_code == 200

    data = json.loads(response.data)
    assert data['success'] is True
    assert data['data'] == []

def test_scheduled_tasks_empty(client):
    """测试空的定时任务列表"""
    response = client.get('/api/scheduled-analysis-status')
    assert response.status_code == 200

    data = json.loads(response.data)
    assert data['success'] is True
    assert data['data'] == []

def test_invalid_api_endpoint(client):
    """测试无效的API端点"""
    response = client.get('/api/nonexistent')
    assert response.status_code == 404

    data = json.loads(response.data)
    assert data['success'] is False
    assert data['error'] == 'NOT_FOUND'

def test_ai_analysis_missing_params(client):
    """测试AI分析缺少参数"""
    response = client.post('/api/ai-analysis',
                          json={},
                          content_type='application/json')
    assert response.status_code == 400

    data = json.loads(response.data)
    assert data['success'] is False